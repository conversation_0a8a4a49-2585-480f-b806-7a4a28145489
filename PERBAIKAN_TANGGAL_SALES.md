# Dokumentasi Perbaikan Masalah <PERSON>gal di Tabel tbl_sales

## Masalah yang Ditemukan

### 1. Inkonsistensi Data Tanggal
- Kolom `sale_date` (varchar) dan `date_time` (timestamp) tidak sinkron
- `date_time` menggunakan timezone server (UTC)
- `sale_date` menggunakan timezone aplikasi (Asia/Singapore)
- Menyebabkan perbedaan tanggal pada transaksi yang dilakukan di malam hari

### 2. Penyebab Masalah
- Database menggunakan `CURRENT_TIMESTAMP` (timezone server)
- Aplikasi menggunakan `date('Y-m-d')` PHP (timezone aplikasi)
- Pengaturan timezone tidak konsisten antara database dan aplikasi

## Solusi yang Diterapkan

### 1. Perbaikan Data Existing
**File:** `fix_sales_date_script.sql`
- Update semua `sale_date` berdasarkan `date_time` dengan timezone Asia/Singapore
- Menggunakan `CONVERT_TZ()` untuk konversi timezone
- Backup data sebelum update (opsional)

```sql
UPDATE tbl_sales 
SET sale_date = DATE(CONVERT_TZ(date_time, '+00:00', '+08:00'))
WHERE del_status = 'Live';
```

### 2. Perbaikan Kode Aplikasi

#### A. Sale.php
**Lokasi:** `application/controllers/Sale.php`
**Perubahan:**
- Line 280-285: Perbaikan insert data sales
- Line 806-808: Perbaikan update data sales
- Line 1584-1598: Perbaikan fungsi change_date_of_a_sale_ajax()

**Prinsip:** 
- Set `date_time` terlebih dahulu
- Derive `sale_date` dari `date_time` untuk konsistensi
- Handle future sales dengan benar

#### B. Waiter_app.php
**Lokasi:** `application/controllers/Waiter_app.php`
**Perubahan:**
- Line 143-145: Perbaikan insert data sales dari waiter app

### 3. Perbaikan Database Schema
**File:** `fix_database_schema.sql`
- Menghilangkan `DEFAULT CURRENT_TIMESTAMP` pada kolom `date_time`
- Membuat aplikasi full control terhadap pengisian tanggal

### 4. Script Validasi
**File:** `validate_sales_data.sql`
- Cek konsistensi data setelah perbaikan
- Analisis distribusi data per tanggal
- Deteksi data yang bermasalah

## Langkah Implementasi

### 1. Backup Data (WAJIB)
```sql
CREATE TABLE tbl_sales_backup AS SELECT * FROM tbl_sales;
```

### 2. Jalankan Script Perbaikan
1. Jalankan `fix_sales_date_script.sql`
2. Jalankan `fix_database_schema.sql` (opsional)
3. Deploy perubahan kode aplikasi
4. Jalankan `validate_sales_data.sql` untuk validasi

### 3. Testing
- Test insert transaksi baru
- Test update tanggal transaksi
- Test laporan berdasarkan tanggal
- Verifikasi konsistensi data

## Timezone Configuration

### Aplikasi menggunakan:
- **Timezone:** Asia/Singapore (UTC+8)
- **Format Date:** Y-m-d
- **Format DateTime:** Y-m-d H:i:s

### Database:
- **Timezone:** Mengikuti aplikasi (tidak lagi auto CURRENT_TIMESTAMP)
- **Kolom date_time:** Diisi manual oleh aplikasi
- **Kolom sale_date:** Derived dari date_time

## Monitoring Pasca Implementasi

### 1. Query Monitoring Harian
```sql
-- Cek konsistensi data hari ini
SELECT 
    COUNT(*) as total_today,
    SUM(CASE WHEN sale_date = DATE(date_time) THEN 1 ELSE 0 END) as consistent_today
FROM tbl_sales 
WHERE DATE(date_time) = CURDATE() 
AND del_status = 'Live';
```

### 2. Alert untuk Data Inconsistent
```sql
-- Data yang perlu dicek manual
SELECT id, sale_no, sale_date, date_time 
FROM tbl_sales 
WHERE sale_date != DATE(date_time) 
AND del_status = 'Live'
AND DATE(date_time) >= CURDATE() - INTERVAL 7 DAY;
```

## Catatan Penting

1. **Timezone Server:** Pastikan server menggunakan timezone yang benar
2. **PHP Timezone:** Aplikasi sudah mengatur timezone via `setDefaultTimezone()`
3. **Future Sales:** Fitur future sales tetap berfungsi dengan benar
4. **Laporan:** Semua laporan berdasarkan tanggal akan konsisten

## Kontak
Jika ada masalah setelah implementasi, hubungi tim development.
