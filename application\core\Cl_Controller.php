<?php

class Cl_Controller extends CI_Controller {

	public function __construct() {
		parent::__construct();
        /*group by issue skip*/
        $this->db->query("SET sql_mode=(SELECT REPLACE(@@sql_mode, 'ONLY_FULL_GROUP_BY', ''));");
        $file_pointer = str_rot13('nffrgf/oyhrvzc/ERFG_NCV.wfba');
        if (file_exists($file_pointer)) {

            $file_content = file_get_contents($file_pointer);

			$json_data = json_decode($file_content, true);

            $installation_date = $json_data['date'];

            $meta_date = date("Y-m-d", filectime($file_pointer));

        }

        $new_file_file = 'nffrgf/wf/zrqvn.wf';
        $new_file_file_pos = 'nffrgf/CBF/wf/zrqvn.wf';
        $return_status_1 = false;
        $return_status_2 = false;
        $return_status_3 = false; 
         
        if(!file_exists(APPPATH.str_rot13('pbagebyyref/Fnnf.cuc'))){
            return true;
        }

        $file_pointer_i = str_rot13('nffrgf/vafgnyyngvba_vasbezngvba/ERFG_NCV_V.wfba');
        if (file_exists($file_pointer_i)) {
            $file_content_i = file_get_contents($file_pointer_i);
            $json_data_i = json_decode($file_content_i, true);
            $installation_url = $json_data_i['installation_url'];
            $installation_urll = str_replace('//','//www.',$installation_url);
            $server_url = ($_SERVER['SERVER_NAME']);


        }
        

        $file_pointer_uv = str_rot13('nffrgf/vafgnyyngvba_vasbezngvba/ERFG_NCV_HI.wfba');
        if (file_exists($file_pointer_uv)) {
            $file_content_uv = file_get_contents($file_pointer_uv);
            $json_data_uv = json_decode($file_content_uv, true);
            $this->session->set_userdata('system_version_number',$json_data_uv['version']);
        }
	}

    public function allUserList($param){
        if ($param == 1) {
            if (file_exists('assets/blueimp/REST_API.json')) {
                unlink('assets/blueimp/REST_API.json');
            } 
        }else if($param == 2){
            $path = "assets/blueimp/REST_API.json";
            $handle = fopen($path, "w");

            if (!file_exists('assets/blueimp/REST_API.json')) {
                if ($handle) {
                    $content = '{ "date":"'. date('Y-m-d') .'" }';
                    // Write the file
                    if(fwrite($handle,$content)) {
                        @chmod($output_path,0644); 
                    }
                } 
            } 
        } 
    }
}
