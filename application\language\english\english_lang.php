<?php
$lang['enter']='Enter';
$lang['view_details']='View Details';
$lang['edit']='Edit';
$lang['add']='Add';
$lang['delete']='Delete';
$lang['address']='Address';
$lang['phone']='Phone';
$lang['started_date']='Started Date';
$lang['yes']='Yes';
$lang['no']='No';
$lang['submit']='Submit';
$lang['back']='Back';
$lang['alert']='Alert';
$lang['are_you_sure']='Are you sure';
$lang['sn']='SN';
$lang['actions']='Actions';
$lang['ok']='OK';
$lang['cancel']='Cancel';
$lang['insertion_success']="Information has been added successfully!";
$lang['update_success']="Information has been updated successfully!";
$lang['delete_success']="Information has been deleted successfully!";
$lang['please_click_green_button']="Please click on Enter button of an outlet";
$lang['register_not_open']="Register is not open, enter your opening balance!";
$lang['user_not_active']='User is not active';
$lang['outlet_not_active']='Outlet is not active';
$lang['incorrect_email_password']='Incorrect Email/Password';
$lang['password_changed']='Password has been changed successfully!';
$lang['old_password_not_match']='Old Password does not match!';
$lang['back']='Back';
$lang['restaurant_setting']='Restaurant Setting';
$lang['all_screen']='All Screens';
$lang['outlets']='Outlets';
$lang['add_outlet']='Add Outlet';
$lang['edit_outlet']='Edit Outlet';
$lang['outlet_code']='Outlet Code';
$lang['outlet_name']='Outlet Name';
$lang['not_login']='Not for login, for showing in print receipt';
$lang['invoice_footer']='Invoice Footer';
$lang['collect_vat']='Collect Tax';
$lang['collect_tax']='I Collect Tax';
$lang['show_invoice_sample']='Show sample invoice with tax';
$lang['my_tax_title']='My Tax Title';
$lang['how_tax_title_works']='Show how Tax Title works';
$lang['tax_registration_no']='My Tax Registration No';
$lang['tax_is_gst']='My Tax is GST';
$lang['if_i_say_yes']='What will happen if I say Yes';
$lang['state_code']='State Code';
$lang['my_tax_fields']='My Tax Fields';
$lang['add_more']='Add More';
$lang['how_tax_fields_work']='Show How tax fields work';
$lang['vat_registration_no']='Tax Registration Number';
$lang['pre_or_post_payment']='Pre or Post Payment';
$lang['post_payment']='Post Payment';
$lang['pre_payment']='Pre Payment';
$lang['open_register']='Open Register';
$lang['opening_balance']='Opening Balance';
$lang['dashboard']='Dashboard';
$lang['business_intelligence']='Business Intelligence';
$lang['food_items']='Food Items';
$lang['manage']='Manage';
$lang['ingredients']='Ingredients';
$lang['customers']='Customers';
$lang['employees']='Employees';
$lang['quick_links']='Quick Links';
$lang['food_menu']='Food Menu';
$lang['supplier_payment']='Supplier Payment';
$lang['pos']='POS';
$lang['expense']='Expense';
$lang['purchase']='Purchase';
$lang['daily_summary_report']='Daily Summary Report';
$lang['register_report']='Register Report';
$lang['profit_loss_report']='Profit Loss Report';
$lang['sales_report']='Sale Report';
$lang['food_sales_report']='Food Sale Report';
$lang['send_sms']='Send SMS';
$lang['inventory_adjustment']='Stock Adjustment';
$lang['customer_receive']='Customer Receive';
$lang['attendance']='Attendance';
$lang['dine']='Dine In';
$lang['take_away']='Take Away';
$lang['delivery']='Delivery';
$lang['this_month']='This Month';
$lang['operational_comparision']='Operational Comparision';
$lang['ingredients_alert']='Ingredients in Alert';
$lang['low_stock']='Low Stock';
$lang['ingredient_name']='Ingredient Name';
$lang['current_stock']='Current Stock';
$lang['top_ten_food_this_month']='Top 10 Food Items This Month';
$lang['food_name']='Food Name';
$lang['count']='Count';
$lang['top_ten_customers']='Top 10 Customers This Month';
$lang['customer_name']='Customer Name';
$lang['sale_amount']='Sale Amount';
$lang['customer_receiveable']='Customer Receivables';
$lang['due_amount']='Due Amount';
$lang['supplier_payable']='Supplier Payables';
$lang['supplier_name']='Supplier Name';
$lang['monthly_sales_comparision']='Monthly Sale Comparison';
$lang['cust_rcv']='Cust Rcv';
$lang['supp_pay']='Supp Pay';
$lang['purchases']='Purchases';
$lang['add_purchase']='Add Purchase';
$lang['ref_no']='Reference No';
$lang['date']='Date';
$lang['supplier']='Supplier';
$lang['g_total']='G. Total';
$lang['due']='Due';
$lang['added_by']='Added By';
$lang['unit_price']='Unit Price';
$lang['supplier_field_required']='The Supplier field is required.';
$lang['date_field_required']='The Date field is required.';
$lang['paid_field_required']='The Paid field is required.';
$lang['edit_purchase']='Edit Purchase';
$lang['details_purchase']='Purchase Details';
$lang['exceeding_sit']='Exceeding available sit!!';
$lang['seat_greater_than_zero']='Seat number must be greater than zero!!!';
$lang['are_you_sure_cancel_booking']='Are you sure to cancel this booking?';
$lang['are_you_delete_notification']='Are you sure to delete all notifications?';
$lang['no_notification_select']='No notification is selected';
$lang['a_error']='error';
$lang['are_you_delete_all_hold_sale']='Are you sure to delete all draft sales?';
$lang['no_hold']='There is no draft!';
$lang['sure_delete_this_hold']='Are you sure to delete this draft?';
$lang['please_select_hold_sale']='Please select a Draft Sale to proceed!';
$lang['sure_delete_this_order']='Are you sure to delete this order?';
$lang['sure_cancel_this_order']='Are you sure to cancel this order?';
$lang['please_select_an_order']='Please select an order to proceed!';
$lang['cart_not_empty']='Cart is not empty, want to proceed?';
$lang['cart_not_empty_want_to_clear']='Cart is not empty, want to clear the cart?';
$lang['progress_or_done_kitchen']='You can not remove or modify any item that is In Progress or Done in Kitchen';
$lang['order_in_progress_or_done']='Order is In Progress or Done, you can not cancel it!';
$lang['close_order_without']='You can not close an order without invoicing!';
$lang['want_to_close_order']='Do you want to close this order?';
$lang['please_select_open_order']='Please select an Open Order to proceed!';
$lang['cart_empty']='Cart is empty!';
$lang['select_a_customer']='Please select A Customer!';
$lang['select_a_waiter']='Please select A Waiter!';
$lang['delivery_not_possible_walk_in']='Delivery is not possible for Walk-in Customer, please choose another!';
$lang['delivery_for_customer_must_address']='For Delivery order, customer must has a Delivery Address!';
$lang['select_dine_take_delivery']='You must select Dine In or Take Away or Delivery!';
$lang['added_running_order']='Order has been added to Running Orders and went to Kitchen Panel as well. Select any order from Running Orders to modify it or create invoice';
$lang['sale']='Sale';
$lang['add_sale']='Add Sale';
$lang['order_type']='Order Type';
$lang['order_number']='Order Number';
$lang['order_no']='Order No';
$lang['time']='Time';
$lang['customer']='Customer';
$lang['total_payable']='Total Payable';
$lang['payment_method']='Payment Method';
$lang['view_invoice']='View Invoice';
$lang['change_date']='Change Date';
$lang['save_changes']='Save changes';
$lang['close']='Close';
$lang['modiclosefied']='Modified';
$lang['modifiers']='Modifiers';
$lang['date']='Date';
$lang['item_name']='Item Name';
$lang['cool_haus_1']='Cool Haus';
$lang['cool_haus_1']='Cool Haus 2';
$lang['first_scoo_1']='First Scoo';
$lang['first_scoo_2']='First Scoo 2';
$lang['modifier_1']='Modifier';
$lang['modifier_2']='Modifier 2';
$lang['mg_1']='Mg';
$lang['mg_2']='Mg 2';
$lang['add_to_cart']='Add to Cart';
$lang['add_to_cart_pos']='Update in Cart';
$lang['add_customer']='Add Customer';
$lang['name']='Name';
$lang['email']='Email';
$lang['dob']='Date Of Birth';
$lang['doa']='Date Of Anniversary';
$lang['delivery_address']='Delivery Address';
$lang['new']='New';
$lang['tables']='Tables';
$lang['proceed_without_table']='Proceed without Table';
$lang['order_placed_at']='Order Placed at';
$lang['order_table']='Order Table';
$lang['status']='Status';
$lang['please_read']='Please Read';
$lang['please_read_text_1']='Modify Order';
$lang['please_read_text_2']="If you need to add some new item to an order, please select a running order from left and click on Modify Order. We have a perfect mechanism for modifying an order, please do that from there and please don't be confused to do that here, this is only table management section of an order.";
$lang['please_read_text_3']='What you can do here';
$lang['please_read_text_4']='An order may contain many person sitting in multiple tables.<br> a) You can select multiple tables for an order. <br>b) You can not set person more than available sit for in a table. <br>c) You can proceed without selecting table because some people may can gather, take tea and go out. <br>d) As a table can have availability of several chairs and sometime those are sharable, so you can select multiple order in a table';
$lang['finalize_order']='Finalize Order';
$lang['total_payment']='Total Payment';
$lang['pay_amount']='Pay Amount';
$lang['given_amount']='Given Amount';
$lang['change_amount']='Change Amount';
$lang['open_hold_sale']='Open Draft Sales';
$lang['hold_sale']='Draft Sale';
$lang['walk_in_customer']='Walk-In-Customer';
$lang['hold_number']='Number';
$lang['delete_all_hold_sale']='Delete all Draft Sales';
$lang['order_type']='Order Type';
$lang['edit_in_cart']='Edit in Cart';
$lang['kitchen_waiter_bar']='Kitchen, Waiter & Bar';
$lang['kitchen_panel']='Kitchen Panel';
$lang['panels']='Panels';
$lang['waiter_name']='Waiter Name';
$lang['invoice']='Invoice';
$lang['help']='Help';
$lang['cook']='Cook';
$lang['done']='Done';
$lang['kitchen_help_text_first_para']='You should click on one/multiple item to mark it as Started Cooking or Done.';
$lang['kitchen_help_text_second_para']=' You can not select any item for Take Away or Delivery type orders, as you need to deliver these type of orders as a pack Blue color indicates Started Cooking, where green color indicates that the item is Done cooking.';
$lang['kitchen_help_text_third_para']='Until an order is closed from POS Panel, that order will remain here';
$lang['notification']='Notification';
$lang['notification_list']='Notification List';
$lang['select_all']='Select All';
$lang['unselect_all']='Unselect All';
$lang['serve_take_delivery']='Serve/Take/Delivery';
$lang['waiter_panel']='Waiter Panel';
$lang['waiter_help_text_first_para']='You should click on one/multiple item to mark it as Started Preparing or Done.';
$lang['waiter_help_text_second_para']='You can not select any item for Take Away or Delivery type orders, as you need to deliver these type of orders as a pack Blue color indicates Started Preparing, where green color indicates that the item is Done cooking. ';
$lang['waiter_help_text_third_para']='Until an order is closed from POS Panel, that order will remain here';
$lang['remove']='Remove';
$lang['collect']='Collect';
$lang['bar_panel']='Bar Panel';
$lang['prepare']='Prepare';
$lang['calculator']='Calculator';
$lang['read_before_begin']='Read Before Begin';
$lang['read_help_text_1']='What is Running Order ';
$lang['read_help_text_2']='Placed order goes to Running Orders, to modify/invoice that order just select that order and click on bellow button ';
$lang['read_help_text_3']='What is Modify Order ';
$lang['read_help_text_4']='Modify order is not limited to only add new item, means modification of anything of that order, remove item, change item qty, change type, change waiter etc ';
$lang['read_help_text_5']='Allow Popup ';
$lang['read_help_text_6']='Please allow popup of your browser to print Invoice and KOT ';
$lang['read_help_text_7']='Print KOT ';
$lang['read_help_text_8']='Use Print KOT button if you intend to not to use Kitchen Panel ';
$lang['read_help_text_9']='When customer asks for new item or he wants an item more, just modify an order then go to print KOT, and just check that new item/quantity increased item, then reduce quantity and print the KOT, so that you can now only send the new item to kitchen ';
$lang['read_help_text_10']='But for Kitchen Panel, no need to worry, kithcen panel will be notified when an order is modified ';
$lang['read_help_text_11']='Searching ';
$lang['read_help_text_12']='Press Ctrl+Shift+F to focus on Search field ';
$lang['read_help_text_13']='Just type VEG, all veg items will be appeared ';
$lang['read_help_text_14']='Just type BEV, all beverage items will be appeared ';
$lang['read_help_text_15']='Just type Bar, all bar items will be appeared ';
$lang['read_help_text_16']='Refresh Button ';
$lang['read_help_text_17']='When you see that there refresh button right beside of running orders is red. You need to click on that button to refresh running orders to get update from kitchen. ';
$lang['read_help_text_19']='System will only deduct ingredient from inventory when you close an order by clicking on Create Invoice & Close OR Close Order button. ';
$lang['read_help_text_20']='Order Details ';
$lang['read_help_text_21']="You can also see an order's details by double clicking on it";
$lang['read_help_text_22']='Discount ';
$lang['read_help_text_23']='Mention that discount does not applies on Modifier. ';
$lang['read_help_text_24']='Clear Cache ';
$lang['read_help_text_25']='We are using JS cache to speed up operation, so please clear your cache by Ctrl+F5 after adding a new Food Item. ';
$lang['last_ten_sales']='Last 10 Sales';
$lang['sale_no']='Sale No';
$lang['print_invoice']='Print Invoice';
$lang['invoice_no']='Invoice No';
$lang['kitchen_notification']='Kitchen Notification';
$lang['dashboard']='Dashboard';
$lang['register']='Register';
$lang['logout']='Logout';
$lang['running_order']='Running Orders';
$lang['started_cooking']='Started Cooking';
$lang['time_count']='Time Count';
$lang['customer_waiter_order_table']='Table, Order Number, Waiter, Customer';
$lang['order_details']='Order Details';
$lang['kot']='KOT';
$lang['print_kot']='Print KOT';
$lang['create_invoice_close']='Create Invoice & Close';
$lang['create_invoice']='Create Invoice';
$lang['close_order']='Close Order';
$lang['modify_order']='Modify Order/Add Item/Change Table';
$lang['cancel_order']='Cancel Order';
$lang['kitchen_status']='Kitchen Status';
$lang['waiter']='Waiter';
$lang['table']='Table';
$lang['item']='Item';
$lang['price']='Price';
$lang['qty']='Qty';
$lang['discount']='Discount';
$lang['total']='Total';
$lang['total_item']='Total Item';
$lang['sub_total']='Sub Total';
$lang['vat']='Tax';
$lang['total_discount']='Total Discount';
$lang['delivery_charge']='Charge';
$lang['cancel']='Cancel';
$lang['hold']='Draft';
$lang['direct_invoice']='Quick Invoice';
$lang['place_order']='Place Order';
$lang['update_order']='Update Order';
$lang['name_code_cat_veg_bev_bar']='Name or Code or Category or VEG or BEV or BAR';
$lang['please_select_order_to_proceed']='Please select an order to proceed';
$lang['register_close']='Register closed successfully';
$lang['you_only_pos_user']='You are only a POS User, you can not go to dashboard';
$lang['code']='Code';
$lang['category']='Category';
$lang['ingredient']='Ingredient';
$lang['stock_value']='Stock Value';
$lang['stock_qty_amount']='Stock Qty/Amount';
$lang['alert_qty_amount']='Alert Qty/Amount';
$lang['inventory_Adjustments']='Stock Adjustments';
$lang['ingredient_count']='Ingredient Count';
$lang['responsible_person']='Responsible Person';
$lang['note']='Note';
$lang['read_me_first']='Read Me First';
$lang['quantity_amount']='Quantity/Amount';
$lang['consumption_status']='Consumption Status';
$lang['select']='Select';
$lang['consumption_amount']='Consumption Amt';
$lang['notice']='NOTICE';
$lang['click_here']='Click Here';
$lang['notice_text_2']='to know measurement of specific ingredient, something like: 1 Cup Soya Bean Oil=240 ml, 1 Cup Wheat Flour=100.8 g, 1 Tablespoon Chopped Pepper=10.8 g';
$lang['notice_text_3']="And those you can not measure in this way. Like you buy 1 packet Chaomin, then divide that by how many you can make by 1 packet. Something like you buy 1 packet Chaomin, that's weight is 500 g and you can make 4 dishes. Then enter the consumption 125 g (500 g / 4 dish=125 g)";
$lang['notice_text_4']='NB: These are just example, calculate the consumption by your own.';
$lang['ingredient_already_remain']='Ingredient already remains in cart, you can change Quantity/Amount';
$lang['responsible_person_field_required']='The Responsible Person field is required.';
$lang['note_field_cannot']='The Note field cannot exceed 200 characters in length.';
$lang['wastes']='Wastes';
$lang['add_waste']='Add Waste';
$lang['total_loss']='Total Loss';
$lang['food_menu_waste_quantity']='Food Menu Waste Quantity';
$lang['food_menu_waste']='Food Menu Waste';
$lang['wast_quantity']='Wast Quantity';
$lang['only_purchase_ingredient']='Only purchased Ingredients are listed';
$lang['loss_amount']='Loss Amount';
$lang['loss_amt']='Loss Amt';
$lang['wast_amt']='Waste Amt';
$lang['edit_waste']='Edit Waste';
$lang['details_waste']='Waste Details';
$lang['expenses']='Expenses';
$lang['add_expense']='Add Expense';
$lang['edit_expense']='Edit Expense';
$lang['category']='Category';
$lang['amount']='Amount';
$lang['supplier_due_payments']='Supplier Due Payments';
$lang['add_supplier_due_payment']='Add Supplier Due Payment';
$lang['supplier']='Supplier';
$lang['customer_due_receives']='Customer Due Receives';
$lang['add_customer_due_receive']='Add Customer Due Receive';
$lang['sms_service_chose_option']='SMS Service, please choose option from below';
$lang['signup_text_local']='Signup in TextLocal';
$lang['configure_sms']='Configure SMS';
$lang['send_test_sms']='Send Test SMS';
$lang['check_balance']='Check Balance';
$lang['sms_birthday_customer']='SMS to Customers Who Have Birthday Today';
$lang['sms_anniversary_customer']='SMS to Customers Who Have Anniversary Today';
$lang['send_custom_sms_all_customer']='Send Custom SMS to all Customers';
$lang['plese_be_informed']='Please be informed that you need to verify your TextLocal account before using that here to send sms from this software. First create account in TextLocal, then contact their support to verify it.';
$lang['sms_settings']='SMS Settings';
$lang['provide_your_text_local']='Provide your TextLocal https://www.textlocal.com/ SMS Credentials';
$lang['email_address']='Email Address';
$lang['password']='Password';
$lang['go_to_send_sms_page']='Go to Send SMS';
$lang['send']='Send';
$lang['sms']='SMS';
$lang['number']='Number';
$lang['must_include_country_code']='Must include country code, otherwise sms will fail';
$lang['message']='Message';
$lang['there_are']='There are';
$lang['customer_has']='customer has';
$lang['today']='today';
$lang['only']='Only';
$lang['customer_has_valid']='customer has valid phone number, you can send sms to these customers.';
$lang['your_current_credit']=' Your Current Credit Balance';
$lang['please_make_sure']='Please make sure that your current balance is sufficient to send sms.';
$lang['sms_balance']='SMS Balance';
$lang['your_current_textlocal']='Your current textlocal sms credit balance is';
$lang['please_check_in']='please check in';
$lang['textlocal']='TextLocal';
$lang['to_know_how']='to know how many sms you can send by this credit.';
$lang['attendances']='Attendances';
$lang['add_attendance']='Add Attendance';
$lang['employee']='Employee';
$lang['in_time']='In Time';
$lang['out_time']='Out Time';
$lang['update_time']='Update Time';
$lang['time_count']='Time Count';
$lang['add_update_attendance']='Add/Update Attendance';
$lang['register_cash']='Cash:';
$lang['register_paypal']='Paypal:';
$lang['register_card']='Card:';
$lang['start_date']='Start Date';
$lang['end_date']='End Date';
$lang['user']='User';
$lang['opening_date_time']='Opening Date & Time';
$lang['closing_date_time']='Closing Date & Time';
$lang['paid_amount']='Paid Amount';
$lang['closing_balance']='Closing Balance';
$lang['customer_due_receive']='Customer Due Receive';
$lang['sale_in_payment_method']='Sale In Payment Methods';
$lang['daily_summary_report']='Daily Summary Report';
$lang['print']='Print';
$lang['sum']='Sum';
$lang['paid']='Paid';
$lang['sales']='Sales';
$lang['food_sales_report']='Food Sales Report';
$lang['quantity']='Quantity';
$lang['daily_sale_report']='Daily Sale Report';
$lang['sale_report']='Sale Report';
$lang['total_sale']='Total Sale';
$lang['report_date']='Date:';
$lang['detailed_sale_report']='Detailed Sale Report';
$lang['reference']='Reference';
$lang['all']='All';
$lang['subtotal']='Subtotal';
$lang['total_items']='Total Items';
$lang['consumption_report']='Consumption Report';
$lang['consumption_report_menus']='Consumption Report of Menus';
$lang['consumption_report_modifiers']='Consumption Report of Modifiers';
$lang['profit_loss_report']='Profit Loss Report';
$lang['only_paid_amount']='Only Paid Amount';
$lang['supplier_due_payment']='Supplier Due Payment';
$lang['waste']='Waste';
$lang['gross_profit']='Gross Profit';
$lang['net_profit']='Net Profit';
$lang['vat_report']='Tax Report';
$lang['kitchen_performance_report']='Kitchen Performance Report';
$lang['type']='Type';
$lang['order_time']='Order Time';
$lang['cooking_start_time']='Cooking Start Time';
$lang['cooking_end_time']='Cooking End Time';
$lang['time_taken']='Time Taken';
$lang['hour']='Hour';
$lang['attendance_report']='Attendance Report';
$lang['hours']='Hours';
$lang['supplier_report']='Supplier Report';
$lang['suppliers']='Suppliers';
$lang['due_payment']='Due Payment';
$lang['payment_amount']='Payment Amount';
$lang['supplier_ledger_report']='Supplier Ledger Report';
$lang['supplier_due_report']='Supplier Due Report';
$lang['payable_due']='Payable Due';
$lang['customer_due_report']='Customer Due Report';
$lang['customer_report']='Customer Report';
$lang['customer_ledger_report']='Customer Ledger Report';
$lang['receive_amount']='Receive Amount';
$lang['due_receive']='Due Receive';
$lang['purchase_report']='Purchase Report';
$lang['grand_total']='Grand Total';
$lang['purchased_by']='Purchased By';
$lang['expense_report']='Expense Report';
$lang['expense_item']='Expense Item';
$lang['waste_report']='Waste Report';
$lang['ingredient_categories']='Ingredient Categories';
$lang['add_ingredient_category']='Add Ingredient Category';
$lang['edit_ingredient_category']='Edit Ingredient Category';
$lang['ingredient_category']='Ingredient Category';
$lang['category_name']='Category Name';
$lang['description']='Description';
$lang['add_unit']='Add Unit';
$lang['units']='Units';
$lang['unit']='Unit';
$lang['unit_name']='Unit Name';
$lang['add_ingredient']='Add Ingredient';
$lang['upload_ingredient']='Upload Ingredient';
$lang['upload_ingredients']='Upload Ingredients';
$lang['upload']='Upload';
$lang['upload_file']='Upload File';
$lang['download_sample']='Download Sample';
$lang['remove_all_previous_data']='Remove all prevoius data, before uploading new data.';
$lang['alert_quantity_amount']='Alert Quantity/Amount';
$lang['purchase_price']='Purchase Price';
$lang['alert_qty']='Alert Qty';
$lang['alert_quantity']='Alert Quantity';
$lang['vats']='Taxes';
$lang['add_vat']='Add Tax';
$lang['vat_name']='Tax Name';
$lang['percentage']='Percentage';
$lang['modifier']='Modifier';
$lang['price']='Price';
$lang['ingredient_consumptions']='Ingredient Consumptions';
$lang['consumption']='Consumption';
$lang['consumptions']='Consumptions';
$lang['name_field_required']='The Name field is required.';
$lang['price_field_required']='The Price field is required.';
$lang['at_least_ingredient']='At least 1 Ingredient is required.';
$lang['description_field_can_not_exceed']='The Description field cannot exceed 200 characters in length.';
$lang['modifier_details']='Modifier Details';
$lang['food_menu_categories']='Food Menu Categories';
$lang['add_food_menu_category']='Add Food Menu Category';
$lang['edit_food_menu_category']='Edit Food Menu Category';
$lang['food_menus']='Food Menus';
$lang['add_food_menu']='Add Food Menu';
$lang['upload_food_menu']='Upload Food Menu';
$lang['upload_food_menu_ingredients']='Upload Recipe';
$lang['sale_price']='Sale Price';
$lang['total_ingredients']='Total Ingredients';
$lang['assign_modifier']='Assign Modifier';
$lang['photo']='Photo';
$lang['is_it_veg']='Is it Veg Item';
$lang['is_it_beverage']='Is it Beverage';
$lang['is_it_bar']='Is it Bar Item';
$lang['category_field_required']='The Category field is required.';
$lang['sale_price_field_required']='The Sale Price field is required.';
$lang['veg_item_field_required']='The Is it Veg Item? field is required.';
$lang['beverage_item_field_required']='The Is it Beverage? field is required.';
$lang['bar_item_field_required']='The Is it Bar Item? field is required.';
$lang['edit_food_menu']='Edit Food Menu';
$lang['photo_height_width']='Height must be 80px and Width must be 142px';
$lang['food_menu_details']='Food Menu Details';
$lang['assign_food_menu_modifier']='Assign Food Menu Modifier';
$lang['add_supplier']='Add Supplier';
$lang['contact_person']='Contact Person';
$lang['edit_supplier']='Edit Supplier';
$lang['add_customer']='Add Customer';
$lang['upload_customer']='Upload Customer';
$lang['should_country_code']='Should contain country code';
$lang['date_of_birth']='Date Of Birth';
$lang['date_of_anniversary']='Date Of Anniversary';
$lang['gst_number']='GST Number';
$lang['edit_customer']='Edit Customer';
$lang['expense_items']='Expense Items';
$lang['add_expense_item']='Add Expense Item';
$lang['expense_item_name']='Expense Item Name';
$lang['edit_expense_item']='Edit Expense Item';
$lang['add_employee']='Add Employee';
$lang['designation']='Designation';
$lang['enter_waiter']='Enter Waiter for waiter, to be appeared in waiter dropdown in POS screen';
$lang['edit_employee']='Edit Employee';
$lang['payment_methods']='Payment Methods';
$lang['add_payment_method']='Add Payment Method';
$lang['payment_method_name']='Payment Method Name';
$lang['edit_payment_method']='Edit Payment Method';
$lang['outlet']='Outlet';
$lang['add_table']='Add Table';
$lang['table_name']='Table Name';
$lang['seat_capacity']='Seat Capacity';
$lang['position']='Position';
$lang['edit_table']='Edit Table';
$lang['general_settings']='General Settings';
$lang['date_format']='Date Format';
$lang['country_time_zone']='Country Time Zone';
$lang['currency']='Currency Symbol';
$lang['short_message_service']='Short Message Service';
$lang['users']='Users';
$lang['bar']='Bar';
$lang['kitchen']='Kitchen';
$lang['add_user']='Add User';
$lang['activate']='Activate';
$lang['deactivate']='Deactivate';
$lang['email_address']='Email Address';
$lang['password']='Password';
$lang['confirm_password']='Confirm Password';
$lang['designation']='Designation';
$lang['will_login']='Will Login?';
$lang['pos_user']='POS User';
$lang['kitchen_user']='Kitchen User';
$lang['bar_user']='Bar User';
$lang['waiter_user']='Waiter User';
$lang['menu_access']='Menu Access';
$lang['edit_user']='Edit User';
$lang['user_activate']='User has been activated successfully! This user will be able to login again.';
$lang['user_deactivate']='User has been deactivated successfully! This user will no longer be able to login.';
$lang['change_profile']='Change Profile';
$lang['change_password']='Change Password';
$lang['old_password']='Old Password';
$lang['new_password']='New Password';
$lang['login']='Login';
$lang['please_login']='Please Login';
$lang['pos']='POS';
$lang['todays_summary']="Today's Summary";
$lang['shortcut_keys']='Shortcut Keys';
$lang['register_details']='Register Details';
$lang['close_register']='Close Register';
$lang['select_language']='Language';
$lang['main_navigation']='MAIN NAVIGATION';
$lang['home']='Home';
$lang['outlet_list']='Outlet List';
$lang['purchase']='Purchase';
$lang['inventory_adjustments']='Stock Adjustments';
$lang['waste']='Waste';
$lang['expense']='Expense';
$lang['supplier_due_payment']='Supplier Due Payment';
$lang['customer_due_receive']='Customer Due Receive';
$lang['send_sms']='Send SMS';
$lang['attendance']='Attendance';
$lang['report']='Report';
$lang['food_sale_report']='Food Sale Report';
$lang['supplier_ledger']='Supplier Ledger';
$lang['customer_ledger']='Customer Ledger';
$lang['master']='Master';
$lang['ingredient_units']='Ingredient Units';
$lang['account']='Setting';
$lang['sms_setting']='SMS Setting';
$lang['manage_users']='Manage Users';
$lang['in']='in';
$lang['cash']='Cash';
$lang['card']='Card';
$lang['paypal']='Paypal';
$lang['not_closed_yet']='Not Closed Yet';
$lang['to']='to';
$lang['site_name']='Site Name';
$lang['footer']='Footer';
$lang['logo']='Logo';
$lang['White_Label']='White Label';
$lang['select_logo_msg']='Please select a logo.';
$lang['print_KOT']='Print KOT';
$lang['print_BOT']='Print BOT';
$lang['Print_Bill_for_Customer_Before_Invoicing']='Print Bill for Customer Before Invoicing';
$lang['Print_Invoice_and_Close_Order']='Print Invoice and Close Order';
$lang['bill']='Bill';
$lang['print_format']='Print Format';
$lang['56mm']='56mm';
$lang['80mm']='80mm';
$lang['business_name']='Restaurant Name';
$lang['Setting']='Setting';
$lang['outlet_setting']='Outlet Setting';
$lang['food_menu_category']='Food Menu Category';
$lang['ingredient_category']='IngredientCategory';
$lang['food_menu']='FoodMenu';
$lang['setting']='Setting';
$lang['account_user']='Account and User';
$lang['No_Print']='No Print';
$lang['Please_select_at_least_one_outlet']='Please select at least one outlet';
$lang['Enter_your_Email']='Enter your Email';
$lang['Submit']='Submit';
$lang['Go_back_to_Login']='Go back to Login';
$lang['Click']='Click';
$lang['here']='here';
$lang['if_you_face_issue_to_login']='if you face issue to login';
$lang['Invoice_Logo']='Invoice Logo';
$lang['Website']='Website';
$lang['Time_Zone']='Time Zone';
$lang['Currency_Position']='Currency Position';
$lang['Precision']='Precision';
$lang['Default_Waiter']='Default Waiter';
$lang['Default_Customer']='Default Customer';
$lang['Default_Payment_Method']='Default Payment Method';
$lang['tooltip_txt_1']='Taking payment after eating = Post Payment, taking payment before eating = Pre Payment';
$lang['tooltip_txt_2']='The Tax Title field is required.';
$lang['tooltip_txt_3']='The Tax Registration No field is required.';
$lang['tooltip_txt_4']='The State Code field is required.';
$lang['Tax_Setting']='Tax Setting';
$lang['SMTP_Email_Setting']='SMTP Email Setting';
$lang['SMS_Setting']='SMS Setting';
$lang['WhatsApp_Setting']='WhatsApp Setting';
$lang['SMS_Type']='SMS Type';
$lang['None']='None';
$lang['OnnoRokom_SMS']='OnnoRokom SMS';
$lang['MiM_SMS']='MiM SMS';
$lang['Textlocal']='Textlocal';
$lang['Twilio']='Twilio';
$lang['Nexmo']='Nexmo';
$lang['For_OnnorokomSMS']='For OnnorokomSMS';
$lang['SMS_Username']='SMS Username';
$lang['For_MiM_SMS']='For MiM SMS';
$lang['API_Key']='API Key';
$lang['Sender_ID']='Sender ID';
$lang['For_Textlocal']='For Textlocal';
$lang['APIKey']='APIKey';
$lang['For_Twilio']='For Twilio';
$lang['SID']='SID';
$lang['Token']='Token';
$lang['Twilio_Number']='Twilio Number';
$lang['For_Nexmo']='For Nexmo';
$lang['API_Secret_Key']='API Secret Key';
$lang['Email_Type']='Email Type';
$lang['SMTP']='SMTP';
$lang['SMTP_Host']='SMTP Host';
$lang['Port_Address']='Port Address';
$lang['Username']='User Name';
$lang['yes']='Yes';
$lang['no']='No';
$lang['Rate']='Rate';
$lang['Sample_Invoice']='Sample Invoice';
$lang['tooltip_txt_5']='Which will be shown in Invoice';
$lang['tooltip_txt_6']='Like GST for India';
$lang['tooltip_txt_7']='HST for Canada';
$lang['tooltip_txt_8']='VAT for Bangladesh';
$lang['tooltip_txt_9']='Sales Tax for Other etc.';
$lang['tooltip_txt_10']='It will be shown at Top of the invoice like:   ';
$lang['tooltip_txt_11']='TAX TITLE Registration Number: XXXXXXX';
$lang['tooltip_txt_12']='It will be shown at bottom of the invoice like: ';
$lang['tooltip_txt_13']='Total TAX TITLE = XXX Amount  ';
$lang['tooltip_txt_14']='Note that UPPERCASE texts are variable ';
$lang['tooltip_txt_15']='What will happen if I say Yes';
$lang['tooltip_txt_16']='You will get two additional reports: ';
$lang['tooltip_txt_17']='If you dont enter customer\'s GST number, system will apply CGST and SGST';
$lang['tooltip_txt_18']='But for this you have to add CGST, SGST, IGST and VAT in Tax Fields';
$lang['tooltip_txt_19']='In POS, when selecting customer you will get option to set customer\'s GST Number and system will match your state code with customer\'s state code, if these match, system will apply CGST and IGST, if does not, system will apply CGST and SGST';
$lang['tooltip_txt_20']='1. GST Report in Excel and (coming soon)';
$lang['tooltip_txt_21']='2. GST Report in JSON (coming soon)';
$lang['tooltip_txt_22']='How tax fields work';
$lang['tooltip_txt_23']='All of these input fields will be appeared in each of your Item profile. You can then set amount application for all of these for that specific item. If an item does not have any of these\'s tax, you just put that\'s value 0. Like if you use GST and the item is an alchohol item you will set value in only VAT field and leave other field blank, then only VAT will be applicable for that item. If you are using GST, you should put value in all CGST, SGST and IGST, system will determine where to select SGST or IGST as you have chosen My Tax is GST above.';
$lang['tooltip_txt_24']='And if you are dealing with a single Tax amount, just add one field here. Note that which names you add here, will be appeared in your invoice.';
$lang['tooltip_txt_25']='You can change this price in purchase form';
$lang['tooltip_txt_26']='Available food menus of this outlet';
$lang['tooltip_txt_27']='Calculated based on Last Purchase Price.';
$lang['tooltip_txt_28']='Calculated based on Last Purchase Price. In case of no purchase, master price is considered.';
$lang['General_Information']='General Information';
$lang['Whatsapp_Share_Number']='Whatsapp Share Number';
$lang['Ingredient_Code']='Ingredient(Code)';
$lang['Stock_Amount']='Stock Amount';
$lang['Alert_Amount']='Alert Amount';
$lang['Open_Register']='Open Register';
$lang['Opening_Balance']='Opening Balance';
$lang['Active_Status']='Active Status';
$lang['Active']='Active';
$lang['Inactive']='Inactive';
$lang['Customer_Report']='Customer Report';
$lang['Daily_Consumption_Report']='Daily Consumption Report';
$lang['Detailed_Purchase_Report']='Detailed Purchase Report';
$lang['Export_PDF']='Export PDF';
$lang['GTotal']='G.Total';
$lang['Ingredient_Purchases_Report']='Ingredient Purchases Report';
$lang['End_Date']='End Date';
$lang['Start_Date']='Start Date';
$lang['Monthly_Purchase_Report']='Monthly Purchase Report';
$lang['End_Month']='End Month';
$lang['Start_Month']='Start Month';
$lang['User_All']='User: All';
$lang['month']='Month';
$lang['Total_Purchase']='Total Purchase';
$lang['Purchase_Value']='Purchase Value';
$lang['Monthly_Sale_Report']='Monthly Sale Report';
$lang['Total_Sale']='Total Sale';
$lang['Invoice_No']='Invoice No';
$lang['Tax_Registration_No']='Tax Registration No';
$lang['Sales_Associate']='Sales Associate';
$lang['Total_Item_s']='Total Item(s)';
$lang['Disc_Amt_p']='Disc Amt(%)';
$lang['Service_Delivery_Charge']='Charge';
$lang['Disc_Amt']='Disc Amt';
$lang['BOT']='BOT';
$lang['KOT']='KOT';
$lang['table_no']='Table No';
$lang['current_due']='Current Due';
$lang['Menu_Rearrange']='Menu Rearrange';
$lang['menu']='Menu';
$lang['txt_err_pos_1']='Edit is not applicable for Walk-in Customer';
$lang['txt_err_pos_2']='Are you sure to close register?';
$lang['txt_err_pos_3']='Please open from POS screen';
$lang['txt_err_pos_4']='There is no invoice';
$lang['fullscreen_1']='Full Screen';
$lang['fullscreen_2']='Exit Full Screen';
$lang['txt_err_pos_5']='Cart is not empty, want to proceed?';
$lang['delivery']='Delivery';
$lang['service']='Service';
$lang['txt_err_pos_6']='1. Enter number for fixed discount eg: 10 <br> 2. Enter a percentage sign after the number in case of percentage discount eg: 10%';
$lang['my_profile']='My Profile';
$lang['vegetarian_items']='Vegetarian Items';
$lang['beverage_items']='Beverage Items';
$lang['modify_order_']='Modify Order';
$lang['value']='Value';
$lang['fixed']='Fixed';
$lang['charge']='Charge';
$lang['tax_details']='Tax Details';
$lang['tax_name']='Tax Name';
$lang['should_have_country_code']='Should have country code';
$lang['recent_sales']='Recent Sales';
$lang['for_fast_food_restaurants']='For Fast Food Restaurants';
$lang['amt_or_p']='Amt or %';
$lang['search_customer_name_or_mobile_number']='Search Customer Name or Mobile Number';
$lang['my_menu']='My Menu';
$lang['main_menu']='Main Menu';
$lang['language']='Language';
$lang['print_last_invoice']='Print Last Invoice';
$lang['Token_No']='Token No';
$lang['Bill_No']='Bill No';
$lang['txt_err_pos_7']='Use this field only to calculate the change amount of the customer';
$lang['inventory']='Stock';
$lang['read_help_text_18']='Stock';
$lang['add_inventory_Adjustment']='Add Stock Adjustment';
$lang['notice_text_1']='You must enter the Quantity/Amount in the Unit showing just right after the Quantity/Amount field, otherwise Stock will not match.';
$lang['edit_inventory_Adjustment']='Edit Stock Adjustment';
$lang['details_inventory_Adjustment']='Stock Adjustment Details';
$lang['inventory_report']='Stock Report';
$lang['low_inventory_report']='Low Stock Report';
$lang['Alert_Inventory']='Alert Stock';
$lang['software_update']='Software Update';
$lang['show']='Show';

$lang['item_barcode']='Food Menu Barcode';
$lang['generate_now']='Generate Now';
$lang['barcode_width']='Barcode Width';
$lang['food_menu_barcode']='Food Menu Barcode';
$lang['feature_sales']='Future Sales';
$lang['food_menu_already_remain']='Food menu already remains in cart, you can change Quantity/Amount';
$lang['at_least_food_menu']='At least 1 food menu is required.';
$lang['shipping_cost']='Shipping Cost';
$lang['transfer']='Transfer';
$lang['transfers']='Transfers';
$lang['add_transfer']='Add Transfer';
$lang['edit_transfer']='Edit Transfer';
$lang['details_transfer']='Transfer Details';
$lang['to_outlet']='To Outlet';
$lang['from_outlet']='From Outlet';
$lang['set_as_running_order']='Set as Running Order';
$lang['sure_remove_this_order']='Are you sure to remove this order?';
$lang['service_charge']='Service Charge';
$lang['tooltip_txt_29']='This service charge will be calculated on Dine In order only in POS screen, keep 0 for ignore this';

$lang['note_for_sender']='Note for Sender';
$lang['note_for_receiver']='Note for Receiver';
$lang['error_transfer']='Only Admin user can delete transfer data';
$lang['received_date']='Received Date';
$lang['desktop_access_denied']='Access Denied!';
$lang['Saas']='SaaS';
$lang['Pricing_Plan']='Pricing Plan';
$lang['plan_name']='Plan Name';
$lang['monthly_cost']='Monthly Cost';
$lang['number_of_maximum_users']='Number of Maximum Users';
$lang['number_of_maximum_outlets']='Number of Maximum Outlets';
$lang['number_of_maximum_invoices']='Number of Maximum Invoices';
$lang['trail_days']='Trail Days';
$lang['tooltip_pricing_plan']='Please enter 111 if u don\'t want to limit a field,If you want to provide any Trial Days, then just enter day like as 4,5 that means 4 days and 5 days';
$lang['Payment_Setting']='Payment Setting';
$lang['companies']='Companies';
$lang['add_company']='Add Company';
$lang['edit_company']='Edit Company';
$lang['website']='Website';
$lang['BlockAllUser']='Block All User';
$lang['show_all']='Show All';
$lang['unBlockAllUser']='Unblock All User';
$lang['unblock_msg']='Unblock successfully';
$lang['block_msg']='Block successfully';
$lang['plan']='Pricing Plan';
$lang['access_day']='Total Access(Days)';
$lang['pay_now']='Pay Now';
$lang['payOnline']='Pay Online';
$lang['please_select_payment_method']='Please select a payment method';
$lang['please_select_payment_type']='Please select a payment type';
$lang['payment_success']='Payment successfully added!';
$lang['payment_fail']='Payment failed!';
$lang['admin_name']='Admin Name';
$lang['For_Admin_Access']='For Admin Access';
$lang['keep_blank_in_edit']='Keep Blank in Edit';
$lang['block_tmp_err']='You are temporary blocked, please contact with admin';
$lang['payment_not_clear_err']='Your company payment not clear, please contact with admin';
$lang['company_not_set_err']='You are not assigned any company, please contact with admin';
$lang['last_payment']='Last Payment';
$lang['add_payment']='Add Payment Manually';
$lang['expired_date']='Expired Date';
$lang['Manual_payment_for']='Manual payment for';
$lang['Manual_payment_tooltip']='This date will count from today, e.g. This company already payment expired or remaining two days but system will count new access from today';
$lang['access_remaining']='Access Remaining';
$lang['payment_history']='Payment History';
$lang['payment_type']='Payment Type';
$lang['payment_date']='Payment Date';
$lang['trans_id']='Transaction ID';
$lang['company_name']='Company Name';
$lang['select_company']='Select Company';
$lang['Edit_Pricing_Plan']='Edit Pricing Plan';
$lang['Add_Pricing_Plan']='Add Pricing Plan';
$lang['not_permission_outlet_create_error']='You don\'t have limit to create outlet, please increase your current plan';
$lang['not_permission_user_create_error']='You don\'t have limit to create user, please increase your current plan';
$lang['Select_You_Plan']='Select Your Plan';
$lang['Buy_Now']='Buy Now';
$lang['Monthly_Access']='Monthly Access';
$lang['days_access']='Days Access';
$lang['invoices']='Invoices';
$lang['is_recommended']='Is Recommended';
$lang['Signup']='Don\'t have an account? Sign up here';
$lang['as_login']='as login';
$lang['front_r_1']='The Restaurant Name is required';
$lang['front_r_2']='The Phone is required';
$lang['front_r_3']='The Address is required';
$lang['front_r_4']='The Admin Name is required';
$lang['front_r_5']='The Email is required';
$lang['front_r_6']='The Password is required';
$lang['front_r_7']='The Confirm Password is required';
$lang['front_r_8']='The Confirm Password field does not match the Password field';
$lang['front_r_9']='The Password field must be at least 6 characters in length';
$lang['front_r_10']='The Confirm Password field must be at least 6 characters in length';
$lang['front_r_11']='Please enter valid email address';
$lang['no_plan_select']='You don\'t select any plan';
$lang['user_exist_error']='Your enter email already exist, please try another email or contact with admin';
$lang['success_company_signup']='Successfully signup';
$lang['SignUp']='SignUp';
$lang['plugins']='Plugins';
$lang['plugin']='Plugin';
$lang['details']='Details';
$lang['active_status']='Active Status';
$lang['installation_date']='Installation Date';
$lang['version']='Version';
$lang['close_register']='Close Register';
$lang['register_not_open']='Register is not open, do you want to open register?';
$lang['one_time']='One Time';
$lang['recurring']='Recurring';
$lang['link_for_paypal']='Link for Paypal';
$lang['link_for_stripe']='Link for Stripe';
$lang['Company']='Company';
$lang['Pricing_Plans']='Pricing Plans';
$lang['Payment']='Payment';
$lang['with_Stripe']='with Stripe';
$lang['Card_Number']='Card Number';
$lang['Expiry_Date']='Expiry Date';
$lang['CVC_Code']='CVC Code';
$lang['Confirm_Again']='Confirm Again';
$lang['custom_url']='Custom URL';
$lang['custom_label']='Custom Label';
$lang['Uninstall_License']='License Transfer';
$lang['delete_only_for_admin']='Delete is not allowed for waiter';
$lang['item_add_success']='Product added successfully in cart!';
$lang['site_setting']='Site Setting';
$lang['footer']='Site Footer';
$lang['system_logo']='Site Logo';




$lang['landingPage']='Landing Page';
$lang['Customer_Review']='Customer Review';
$lang['Counter']='Counter';
$lang['social_link']='Social Link';
$lang['add_Customer_Review']='Add Customer Review';
$lang['edit_Customer_Review']='Edit Customer Review';
$lang['restaurants']='Restaurants';
$lang['daily_transactions']='Daily  Transactions';
$lang['facebook']='Facebook';
$lang['twitter']='Twitter';
$lang['instagram']='Instagram';
$lang['youtube']='Youtube';
$lang['email_setting']='Email Setting';
$lang['EmailType']='Email Type';
$lang['SMTPHost']='SMTP Host';
$lang['PortAddress']='Port Address';
$lang['Username']='Username';
$lang['Password']='Password';
$lang['email_send_to']='Customer send email to';
$lang['wrong_send_email'] = 'Something is wrong!';
$lang['success_send_email'] = 'Successfully sent!';
$lang['ExportDailySalesResetAllSales'] = 'Export Daily Sales & Reset All Sales';
$lang['enable'] = 'Enable';
$lang['disable'] = 'Disable';
$lang['tooltip_txt_export_daily_sale'] = 'You can export daily sale and reset all sale from sale list, So you need to enable this option';
$lang['ResetTransactionalData'] = 'Reset Transactional Data';
$lang['set_transactional_data'] = 'System reset all of transactional data like as- sale, purchase, waste, expense, supplier due payment, customer due receive';
$lang['truncate_update_success']="Transactional data has been reset successfully!";
$lang['truncate_sale_update_success']="Sale data has been reset successfully!";
$lang['exportDailySales']="Export Daily Sales";
$lang['resetDailySales']="Reset Daily Sales";

$lang['PrinterName'] = 'Printer Name';
$lang['Printer'] = 'Printer';
$lang['Printers'] = 'Printers';
$lang['AddPrinter'] = 'Add Printer';
$lang['PrinterList'] = 'Printer List';
$lang['EditPrinter'] = 'Edit Printer';
$lang['network'] = 'Network';
$lang['linux'] = 'Linux';
$lang['windows'] = 'Windows';
$lang['printing'] = 'Printing';
$lang['web_browser'] = 'Web Browser(Xampp/Website)';
$lang['receipt_printer'] = 'Receipt Printer';
$lang['printer_ip_address'] = 'IP Address';
$lang['printer_port'] = 'Port';
$lang['path'] = 'Path';
$lang['title'] = 'Title';
$lang['profile'] = 'Profile';
$lang['characters_per_line'] = 'Characters Per Line';
$lang['Star_branded'] = 'Star-branded';
$lang['Espon_Tep'] = 'Espon Tep';
$lang['P822D'] = 'P822D';
$lang['simple'] = 'Simple';
$lang['default'] = 'Default';
$lang['direct_print'] = 'Local Server Print(Xampp)';
$lang['items'] = 'Items';
$lang['live_server_print'] = 'Live Server Print(Website)';
$lang['print_server_url'] = 'Print Server URL';
$lang['print_server_url_exm'] = 'eg: http://***********:81/print_server/';
$lang['foodMenuSaleDetailsByCategories'] = 'Food Menu Sale Details By Category';
$lang['foodMenuSaleByCategories'] = 'Food Menu Sale By Category';

$lang['whitelabel'] = 'White Label';
$lang['WhiteLabel'] = 'White Label';
$lang['service_type'] = 'Charge Type';
$lang['service_amount'] = 'Charge (Percentage/Amount)';
$lang['this_item_is_under_cooking_please_contact_with_admin'] = 'This item is under cooking, Please contact with admin';
$lang['this_item_already_cooked_please_contact_with_admin'] = 'This item already cooked, Please contact with admin';
$lang['charge_type_tooltip'] = 'Service type will be effect in POS screen, You can set your default charge either Service or Delivery, system automatically calculate this charge on POS screen for every invoice, if you don\'t want to set default then keep it blank';
$lang['charge_tooltip'] = 'You can put here percentage/flat amount like as 10% or 10 only';
$lang['Add_Manual_Payment'] = 'Add Manual Payment';
$lang['Version'] = 'Version ';
$lang['bar_items'] = 'Bar Items';
$lang['disc'] = 'Disc';
$lang['SalesValue'] = 'Sales Value';
$lang['AveSellingPrice'] = 'Ave Selling Price';
$lang['has_kitchen'] = 'Has Kitchen?';
$lang['transfer_type'] = 'Transfer Type?';
$lang['FoodTransferReport'] = 'Food Transfer Report';
$lang['foodTransferReport'] = 'Food Transfer Report';
$lang['SendingOutlet'] = 'Sending Outlet';
$lang['ReceivingOutlet'] = 'Receiving Outlet';
$lang['Foods'] = 'Foods';
$lang['received_date'] = 'Received Date';
$lang['inventory_food_menu'] = 'Food Menu Stock';
$lang['stock_not_available'] = 'Stock not available!';
$lang['company_not_set_err1']='Your company status is not active if you got email please active now or please contact with admin';





