<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitb301ad9255e4b30535ef1326b33dee2a
{
    public static $prefixLengthsPsr4 = array (
        'M' => 
        array (
            '<PERSON>42\\' => 7,
        ),
    );

    public static $prefixDirsPsr4 = array (
        '<PERSON>42\\' => 
        array (
            0 => __DIR__ . '/..' . '/mike42/gfx-php/src/Mike42',
            1 => __DIR__ . '/..' . '/mike42/escpos-php/src/Mike42',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitb301ad9255e4b30535ef1326b33dee2a::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitb301ad9255e4b30535ef1326b33dee2a::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInitb301ad9255e4b30535ef1326b33dee2a::$classMap;

        }, null, ClassLoader::class);
    }
}
