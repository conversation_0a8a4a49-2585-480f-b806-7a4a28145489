{"packages": [{"name": "mike42/escpos-php", "version": "v3.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/mike42/escpos-php.git", "reference": "dcb569a123d75f9f6a4a927aae7625ca6b7fdcf3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mike42/escpos-php/zipball/dcb569a123d75f9f6a4a927aae7625ca6b7fdcf3", "reference": "dcb569a123d75f9f6a4a927aae7625ca6b7fdcf3", "shasum": ""}, "require": {"ext-intl": "*", "ext-json": "*", "ext-zlib": "*", "mike42/gfx-php": "^0.6", "php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": "^6.5", "squizlabs/php_codesniffer": "^3.3"}, "suggest": {"ext-gd": "Used for image printing if present.", "ext-imagick": "Will be used for image printing if present. Required for PDF printing or use of custom fonts."}, "time": "2019-10-13T06:27:43+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Mike42\\": "src/Mike42"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP receipt printer library for use with ESC/POS-compatible thermal and impact printers", "homepage": "https://github.com/mike42/escpos-php", "keywords": ["<PERSON><PERSON><PERSON>", "barcode", "escpos", "printer", "receipt-printer"], "support": {"issues": "https://github.com/mike42/escpos-php/issues", "source": "https://github.com/mike42/escpos-php/tree/v3.0"}, "install-path": "../mike42/escpos-php"}, {"name": "mike42/gfx-php", "version": "v0.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/mike42/gfx-php.git", "reference": "ed9ded2a9298e4084a9c557ab74a89b71e43dbdb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mike42/gfx-php/zipball/ed9ded2a9298e4084a9c557ab74a89b71e43dbdb", "reference": "ed9ded2a9298e4084a9c557ab74a89b71e43dbdb", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpbench/phpbench": "@dev", "phpunit/phpunit": "^6.5", "squizlabs/php_codesniffer": "^3.3.1"}, "time": "2019-10-05T02:44:33+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Mike42\\": "src/Mike42"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The pure PHP graphics library", "homepage": "https://github.com/mike42/gfx-php", "support": {"issues": "https://github.com/mike42/gfx-php/issues", "source": "https://github.com/mike42/gfx-php/tree/v0.6"}, "install-path": "../mike42/gfx-php"}], "dev": true, "dev-package-names": []}