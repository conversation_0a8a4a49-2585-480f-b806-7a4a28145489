@!0Height and bar width
! Default look
kEABC
Height 1
hkEABC
Height 2
hkEABC
Height 4
hkEABC
Height 8
hkEABC
Height 16
hkEABC
Height 32
h kEABC
Width 1
wkEA<PERSON>
Width 2
wkEABC
Width 3
wkEABC
Width 4
wkEABC
Width 5
wkEABC
Width 6
wkEABC
Width 7
wkEABC
Width 8
wkEABC
h(w!0Text position
! No text
H kC012345678901
Above
HkC012345678901
Below
HkC012345678901
Both
HkC012345678901
H!0UPC-A
! Fixed-length numeric product barcodes.

E12 char numeric including (wrong) check digit.
E Content: 012345678901
kA012345678901
ESend 11 chars to add check digit automatically.
E Content: 01234567890
kA01234567890
!0UPC-E
! Fixed-length numeric compact product barcodes.

E6 char numeric - auto check digit & NSC
E Content: 123456
kB123456
E7 char numeric - auto check digit
E Content: 0123456
kB0123456
E8 char numeric
E Content: 01234567
kB01234567
E11 char numeric - auto check digit
E Content: 01234567890
kB01234567890
E12 char numeric including (wrong) check digit
E Content: 012345678901
kB012345678901
!0JAN13/EAN13
! Fixed-length numeric barcodes.

E12 char numeric - auto check digit
E Content: 012345678901
kC012345678901
E13 char numeric including (wrong) check digit
E Content: 0123456789012
kC
0123456789012
!0JAN8/EAN8
! Fixed-length numeric barcodes.

E7 char numeric - auto check digit
E Content: 0123456
kD0123456
E8 char numeric including (wrong) check digit
E Content: 01234567
kD01234567
!0Code39
! Variable length alphanumeric w/ some special chars.

EText, numbers, spaces
E Content: ABC 012
kEABC 012
ESpecial characters
E Content: $%+-./
kE$%+-./
EExtra char (*) Used as start/stop
E Content: *TEXT*
kE*TEXT*
!0ITF
! Variable length numeric w/even number of digits,
as they are encoded in pairs.

ENumeric- even number of digits
E Content: 0123456789
kF
0123456789
!0Codabar
! Varaible length numeric with some allowable
extra characters. ABCD/abcd must be used as
start/stop characters (one at the start, one
at the end) to distinguish between barcode
applications.

ENumeric w/ A A start/stop. 
E Content: A012345A
kGA012345A
EExtra allowable characters
E Content: A012$+-./:A
kGA012$+-./:A
!0Code93
! Variable length- any ASCII is available

EText
E Content: 012abcd
kH012abcd
!0Code128
! Variable length- any ASCII is available

ECode set A uppercase & symbols
E Content: {A012ABCD
kI	{A012ABCD
ECode set B general text
E Content: {B012ABCDabcd
kI
{B012ABCDabcd
ECode set C compact numbers
 Sending chr(21) chr(32) chr(43)
E Content: {C? +
kI{C +
VA