<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit07cf958015b53c7f87ea1501385bcf3f
{
    public static $prefixLengthsPsr4 = array (
        'P' => 
        array (
            'Psr\\Log\\' => 8,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Psr\\Log\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/log/Psr/Log',
        ),
    );

    public static $prefixesPsr0 = array (
        'P' => 
        array (
            'PayPal' => 
            array (
                0 => __DIR__ . '/..' . '/paypal/rest-api-sdk-php/lib',
            ),
        ),
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit07cf958015b53c7f87ea1501385bcf3f::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit07cf958015b53c7f87ea1501385bcf3f::$prefixDirsPsr4;
            $loader->prefixesPsr0 = ComposerStaticInit07cf958015b53c7f87ea1501385bcf3f::$prefixesPsr0;

        }, null, ClassLoader::class);
    }
}
