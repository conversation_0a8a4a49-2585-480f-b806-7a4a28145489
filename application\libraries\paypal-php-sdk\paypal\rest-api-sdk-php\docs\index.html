<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="description" content="">
        <meta name="author" content="">
        <link rel="icon" href="https://www.paypalobjects.com/webstatic/icon/favicon.ico">
        <title>PayPal PHP SDK - Welcome</title>
        <!-- Bootstrap core CSS -->
        <link href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.2/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">
        <!-- Custom styles for this template -->
        <link href="cover.css" rel="stylesheet">

        <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
        <!--[if lt IE 9]>
        <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
        <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
        <![endif]-->
    </head>

    <body>
        <div class="container-fluid body-content">
            <div class="row-fluid header">
                <img src="https://raw.githubusercontent.com/wiki/paypal/PayPal-PHP-SDK/images/homepage.jpg"
                     class="img-responsive">
            </div>
            <div class="container content">
                <div class="row-fluid">
                    <h2>// PayPal PHP SDK <a href="https://github.com/paypal/PayPal-PHP-SDK" title="Github Repository"><i class="fa fa-github"></i></a></h2><hr />
                    <p class="lead">
                        PayPal PHP SDK is our official Open Source PHP SDK for supporting PayPal Rest APIs. Checkout all the supporting documents, samples, codebase from the following links
                    </p>
                </div><br /><br />
                <div class="row clearfix">
                    <a href="https://github.com/paypal/PayPal-PHP-SDK/wiki">
                        <div class="col-md-4">
                            <div class="well clearfix">
                                <div class="sprite"><i class="fa fa-book fa-4x"></i></div>
                                <div class="box"><h3>PHP SDK Wiki</h3>
                                    <hr/>
                                    <p> Find everything from Installing, running Samples, Configurations in PHP SDK Wiki </p>
                                </div>
                            </div>
                        </div>
                    </a>
                    <a href="http://paypal.github.io/PayPal-PHP-SDK/docs/">
                        <div class="col-md-4">
                            <div class="well clearfix">
                                <div class="sprite"><i class="fa fa-code fa-4x"></i></div>
                                <div class="box"><h3>PHP Source Docs</h3>
                                    <hr/>
                                    <p> Check out PHP Source Documentation, to see the internals of PHP SDK. </p></div>
                            </div>
                        </div>
                    </a>
                    <a href="http://paypal.github.io/PayPal-PHP-SDK/sample/">
                        <div class="col-md-4">
                            <div class="well clearfix">
                                <div class="sprite"><i class="fa fa-terminal fa-4x"></i></div>
                                <div class="box"><h3>PHP Sample Code</h3>
                                    <hr/>
                                    <p> Check out the sample code for using SDKs to call all PayPal APIs. </p></div>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="row clearfix">
                    <a href="https://developer.paypal.com/webapps/developer/docs/api/">
                        <div class="col-md-4">
                            <div class="well clearfix">
                                <div class="sprite"><i class="fa fa-paypal fa-4x"></i></div>
                                <div class="box"><h3>PayPal API Reference</h3>
                                    <hr/>
                                    <p> Checkout the Official PayPal REST API Reference, explaining all API Models</p></div>
                            </div>
                        </div>
                    </a>
                    <a href="https://github.com/paypal/PayPal-PHP-SDK/releases">
                        <div class="col-md-4">
                            <div class="well clearfix">
                                <div class="sprite"><i class="fa fa-tags fa-4x"></i></div>
                                <div class="box"><h3>PHP SDK Releases</h3>
                                    <hr/>
                                    <p>Download the latest PHP SDK Release</p></div>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
        <footer class="footer">
            <div class="container">
                <div class="footer-div">
                    <ul class="footer-links">
                        <li>
                            <a href="https://github.com/paypal/PayPal-PHP-SDK" target="_blank"><i
                                    class="fa fa-github"></i>
                                Github</a></li>
                        <li>
                            <a href="https://developer.paypal.com/webapps/developer/docs/api/" target="_blank"><i
                                    class="fa fa-book"></i> PayPal API Reference</a>
                        </li>
                        <li>
                            <a href="https://github.com/paypal/PayPal-PHP-SDK/issues" target="_blank"><i
                                    class="fa fa-exclamation-triangle"></i> Report Issues </a>
                        </li>

                    </ul>
                </div>
            </div>
        </footer>

        <!-- Bootstrap core JavaScript
        ================================================== -->
        <!-- Placed at the end of the document so the pages load faster -->
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>
        <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.2/js/bootstrap.min.js"></script>
    </body>
</html>
