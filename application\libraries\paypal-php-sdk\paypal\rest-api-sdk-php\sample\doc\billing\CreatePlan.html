<!DOCTYPE html><html lang="en"><head><title>billing/CreatePlan</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="billing/CreatePlan"><meta name="groc-project-path" content="billing/CreatePlan.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">billing/CreatePlan.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="create-plan-sample">Create Plan Sample</h1>
<p>This sample code demonstrate how you can create a billing plan, as documented here at:
<a href="https://developer.paypal.com/webapps/developer/docs/api/#create-a-plan">https://developer.paypal.com/webapps/developer/docs/api/#create-a-plan</a>
API used: /v1/payments/billing-plans</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">require</span> <span class="hljs-keyword">__DIR__</span> . <span class="hljs-string">'/../bootstrap.php'</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">ChargeModel</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Currency</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">MerchantPreferences</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">PaymentDefinition</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Plan</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Create a new instance of Plan object</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$plan</span> = <span class="hljs-keyword">new</span> Plan();</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="basic-information">Basic Information</h1>
<p>Fill up the basic information that is required for the plan</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$plan</span>-&gt;setName(<span class="hljs-string">'T-Shirt of the Month Club Plan'</span>)
    -&gt;setDescription(<span class="hljs-string">'Template creation.'</span>)
    -&gt;setType(<span class="hljs-string">'fixed'</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="payment-definitions-for-this-billing-plan">Payment definitions for this billing plan.</h1></div></div></div><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-variable">$paymentDefinition</span> = <span class="hljs-keyword">new</span> PaymentDefinition();</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>The possible values for such setters are mentioned in the setter method documentation.
Just open the class file. e.g. lib/PayPal/Api/PaymentDefinition.php and look for setFrequency method.
You should be able to see the acceptable values in the comments.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$paymentDefinition</span>-&gt;setName(<span class="hljs-string">'Regular Payments'</span>)
    -&gt;setType(<span class="hljs-string">'REGULAR'</span>)
    -&gt;setFrequency(<span class="hljs-string">'Month'</span>)
    -&gt;setFrequencyInterval(<span class="hljs-string">"2"</span>)
    -&gt;setCycles(<span class="hljs-string">"12"</span>)
    -&gt;setAmount(<span class="hljs-keyword">new</span> Currency(<span class="hljs-keyword">array</span>(<span class="hljs-string">'value'</span> =&gt; <span class="hljs-number">100</span>, <span class="hljs-string">'currency'</span> =&gt; <span class="hljs-string">'USD'</span>)));</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Charge Models</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$chargeModel</span> = <span class="hljs-keyword">new</span> ChargeModel();
<span class="hljs-variable">$chargeModel</span>-&gt;setType(<span class="hljs-string">'SHIPPING'</span>)
    -&gt;setAmount(<span class="hljs-keyword">new</span> Currency(<span class="hljs-keyword">array</span>(<span class="hljs-string">'value'</span> =&gt; <span class="hljs-number">10</span>, <span class="hljs-string">'currency'</span> =&gt; <span class="hljs-string">'USD'</span>)));

<span class="hljs-variable">$paymentDefinition</span>-&gt;setChargeModels(<span class="hljs-keyword">array</span>(<span class="hljs-variable">$chargeModel</span>));

<span class="hljs-variable">$merchantPreferences</span> = <span class="hljs-keyword">new</span> MerchantPreferences();
<span class="hljs-variable">$baseUrl</span> = getBaseUrl();</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>ReturnURL and CancelURL are not required and used when creating billing agreement with payment_method as &quot;credit_card&quot;.
However, it is generally a good idea to set these values, in case you plan to create billing agreements which accepts &quot;paypal&quot; as payment_method.
This will keep your plan compatible with both the possible scenarios on how it is being used in agreement.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$merchantPreferences</span>-&gt;setReturnUrl(<span class="hljs-string">"$baseUrl/ExecuteAgreement.php?success=true"</span>)
    -&gt;setCancelUrl(<span class="hljs-string">"$baseUrl/ExecuteAgreement.php?success=false"</span>)
    -&gt;setAutoBillAmount(<span class="hljs-string">"yes"</span>)
    -&gt;setInitialFailAmountAction(<span class="hljs-string">"CONTINUE"</span>)
    -&gt;setMaxFailAttempts(<span class="hljs-string">"0"</span>)
    -&gt;setSetupFee(<span class="hljs-keyword">new</span> Currency(<span class="hljs-keyword">array</span>(<span class="hljs-string">'value'</span> =&gt; <span class="hljs-number">1</span>, <span class="hljs-string">'currency'</span> =&gt; <span class="hljs-string">'USD'</span>)));


<span class="hljs-variable">$plan</span>-&gt;setPaymentDefinitions(<span class="hljs-keyword">array</span>(<span class="hljs-variable">$paymentDefinition</span>));
<span class="hljs-variable">$plan</span>-&gt;setMerchantPreferences(<span class="hljs-variable">$merchantPreferences</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>For Sample Purposes Only.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$request</span> = <span class="hljs-keyword">clone</span> <span class="hljs-variable">$plan</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="create-plan">Create Plan</h3></div></div></div><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$output</span> = <span class="hljs-variable">$plan</span>-&gt;create(<span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Created Plan"</span>, <span class="hljs-string">"Plan"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$request</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Created Plan"</span>, <span class="hljs-string">"Plan"</span>, <span class="hljs-variable">$output</span>-&gt;getId(), <span class="hljs-variable">$request</span>, <span class="hljs-variable">$output</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$output</span>;</div></div></div></div></body></html>
