<!DOCTYPE html><html lang="en"><head><title>billing/DeletePlan</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="billing/DeletePlan"><meta name="groc-project-path" content="billing/DeletePlan.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">billing/DeletePlan.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="delete-plan-sample">Delete Plan Sample</h1>
<p>This sample code demonstrate how you can delete a billing plan, as documented here at:
<a href="https://developer.paypal.com/webapps/developer/docs/api/#retrieve-a-plan">https://developer.paypal.com/webapps/developer/docs/api/#retrieve-a-plan</a>
API used: /v1/payments/billing-plans</p></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Retrieving the Plan object from Create Plan Sample</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> Plan $createdPlan */</span>
<span class="hljs-variable">$createdPlan</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'CreatePlan.php'</span>;

<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Plan</span>;

<span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$result</span> = <span class="hljs-variable">$createdPlan</span>-&gt;delete(<span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Deleted a Plan"</span>, <span class="hljs-string">"Plan"</span>, <span class="hljs-variable">$createdPlan</span>-&gt;getId(), <span class="hljs-keyword">null</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Deleted a Plan"</span>, <span class="hljs-string">"Plan"</span>, <span class="hljs-variable">$createdPlan</span>-&gt;getId(), <span class="hljs-keyword">null</span>, <span class="hljs-keyword">null</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$createdPlan</span>;</div></div></div></div></body></html>
