<!DOCTYPE html><html lang="en"><head><title>billing/ExecuteAgreement</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="billing/ExecuteAgreement"><meta name="groc-project-path" content="billing/ExecuteAgreement.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">billing/ExecuteAgreement.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="execute-agreement">Execute Agreement</h1>
<p>This is the second part of CreateAgreement Sample.
Use this call to execute an agreement after the buyer approves it</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">require</span> <span class="hljs-keyword">__DIR__</span> . <span class="hljs-string">'/../bootstrap.php'</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h2 id="approval-status">Approval Status</h2>
<p>Determine if the user accepted or denied the request</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">if</span> (<span class="hljs-keyword">isset</span>(<span class="hljs-variable">$_GET</span>[<span class="hljs-string">'success'</span>]) &amp;&amp; <span class="hljs-variable">$_GET</span>[<span class="hljs-string">'success'</span>] == <span class="hljs-string">'true'</span>) {
    <span class="hljs-variable">$token</span> = <span class="hljs-variable">$_GET</span>[<span class="hljs-string">'token'</span>];
    <span class="hljs-variable">$agreement</span> = <span class="hljs-keyword">new</span> \PayPal\Api\Agreement();
    <span class="hljs-keyword">try</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h2 id="execute-agreement">Execute Agreement</h2>
<p>Execute the agreement by passing in the token</p></div></div><div class="code"><div class="wrapper">        <span class="hljs-variable">$agreement</span>-&gt;execute(<span class="hljs-variable">$token</span>, <span class="hljs-variable">$apiContext</span>);
    } <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">        ResultPrinter::printError(<span class="hljs-string">"Executed an Agreement"</span>, <span class="hljs-string">"Agreement"</span>, <span class="hljs-variable">$agreement</span>-&gt;getId(), <span class="hljs-variable">$_GET</span>[<span class="hljs-string">'token'</span>], <span class="hljs-variable">$ex</span>);
        <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
    }</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printResult(<span class="hljs-string">"Executed an Agreement"</span>, <span class="hljs-string">"Agreement"</span>, <span class="hljs-variable">$agreement</span>-&gt;getId(), <span class="hljs-variable">$_GET</span>[<span class="hljs-string">'token'</span>], <span class="hljs-variable">$agreement</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h2 id="get-agreement">Get Agreement</h2>
<p>Make a get call to retrieve the executed agreement details</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-keyword">try</span> {
        <span class="hljs-variable">$agreement</span> = \PayPal\Api\Agreement::get(<span class="hljs-variable">$agreement</span>-&gt;getId(), <span class="hljs-variable">$apiContext</span>);
    } <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">        ResultPrinter::printError(<span class="hljs-string">"Get Agreement"</span>, <span class="hljs-string">"Agreement"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$ex</span>);
        <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
    }</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printResult(<span class="hljs-string">"Get Agreement"</span>, <span class="hljs-string">"Agreement"</span>, <span class="hljs-variable">$agreement</span>-&gt;getId(), <span class="hljs-keyword">null</span>, <span class="hljs-variable">$agreement</span>);
} <span class="hljs-keyword">else</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printResult(<span class="hljs-string">"User Cancelled the Approval"</span>, <span class="hljs-keyword">null</span>);
}</div></div></div></div></body></html>
