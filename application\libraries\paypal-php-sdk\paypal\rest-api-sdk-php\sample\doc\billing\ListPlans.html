<!DOCTYPE html><html lang="en"><head><title>billing/ListPlans</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="billing/ListPlans"><meta name="groc-project-path" content="billing/ListPlans.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">billing/ListPlans.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="get-list-of-plan-sample">Get List of Plan Sample</h1>
<p>This sample code demonstrate how you can get a list of billing plan, as documented here at:
<a href="https://developer.paypal.com/webapps/developer/docs/api/#list-plans">https://developer.paypal.com/webapps/developer/docs/api/#list-plans</a>
API used: /v1/payments/billing-plans</p></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Retrieving the Plan object from Create Plan Sample to demonstrate the List</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> Plan $createdPlan */</span>
<span class="hljs-variable">$createdPlan</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'CreatePlan.php'</span>;

<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Plan</span>;

<span class="hljs-keyword">try</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Get the list of all plans
You can modify different params to change the return list.
The explanation about each pagination information could be found here
at <a href="https://developer.paypal.com/webapps/developer/docs/api/#list-plans">https://developer.paypal.com/webapps/developer/docs/api/#list-plans</a></p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$params</span> = <span class="hljs-keyword">array</span>(<span class="hljs-string">'page_size'</span> =&gt; <span class="hljs-string">'2'</span>);
    <span class="hljs-variable">$planList</span> = Plan::all(<span class="hljs-variable">$params</span>, <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"List of Plans"</span>, <span class="hljs-string">"Plan"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$params</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"List of Plans"</span>, <span class="hljs-string">"Plan"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$params</span>, <span class="hljs-variable">$planList</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$planList</span>;</div></div></div></div></body></html>
