<!DOCTYPE html><html lang="en"><head><title>billing/SearchBillingTransactions</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="billing/SearchBillingTransactions"><meta name="groc-project-path" content="billing/SearchBillingTransactions.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">billing/SearchBillingTransactions.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="search-billing-transactions-sample">Search Billing Transactions Sample</h1>
<p>This sample code demonstrate how you can search all billing transactions, as documented here at:
<a href="https://developer.paypal.com/webapps/developer/docs/api/#search-for-transactions">https://developer.paypal.com/webapps/developer/docs/api/#search-for-transactions</a>
API used: GET /v1/payments/billing-agreements/<Agreement-Id>/transactions? start-date=yyyy-mm-dd&amp;end-date=yyyy-mm-dd</p></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Retrieving the Agreement object from Get Billing Agreement. This may not be necessary if you are trying to search for transactions of already created Agreement.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> Agreement $agreement */</span>
<span class="hljs-variable">$agreement</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'GetBillingAgreement.php'</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Replace this with your AgreementId to search transactions based on your agreement.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$agreementId</span> = <span class="hljs-variable">$agreement</span>-&gt;getId();

<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Agreement</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Adding Params to search transaction within a given time frame.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$params</span> = <span class="hljs-keyword">array</span>(<span class="hljs-string">'start_date'</span> =&gt; date(<span class="hljs-string">'Y-m-d'</span>, strtotime(<span class="hljs-string">'-15 years'</span>)), <span class="hljs-string">'end_date'</span> =&gt; date(<span class="hljs-string">'Y-m-d'</span>, strtotime(<span class="hljs-string">'+5 days'</span>)));

<span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$result</span> = Agreement::searchTransactions(<span class="hljs-variable">$agreementId</span>, <span class="hljs-variable">$params</span>, <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Search for Transactions"</span>, <span class="hljs-string">"AgreementTransaction"</span>, <span class="hljs-variable">$agreementId</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Search for Transactions"</span>, <span class="hljs-string">"AgreementTransaction"</span>, <span class="hljs-variable">$agreementId</span>, <span class="hljs-variable">$params</span>, <span class="hljs-variable">$result</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$agreement</span>;</div></div></div></div></body></html>
