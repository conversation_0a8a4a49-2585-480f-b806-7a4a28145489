<!DOCTYPE html><html lang="en"><head><title>billing/UpdatePlan</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="billing/UpdatePlan"><meta name="groc-project-path" content="billing/UpdatePlan.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">billing/UpdatePlan.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="update-a-plan">Update a plan</h1>
<p>This sample code demonstrate how you can update a billing plan, as documented here at:
<a href="https://developer.paypal.com/webapps/developer/docs/api/#update-a-plan">https://developer.paypal.com/webapps/developer/docs/api/#update-a-plan</a>
API used:  /v1/payments/billing-plans/<Plan-Id></p></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="making-plan-active">Making Plan Active</h3>
<p>This example demonstrate how you could activate the Plan.</p></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Retrieving the Plan object from Create Plan Sample to demonstrate the List</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> Plan $createdPlan */</span>
<span class="hljs-variable">$createdPlan</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'CreatePlan.php'</span>;

<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Patch</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">PatchRequest</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Plan</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Common</span>\<span class="hljs-title">PayPalModel</span>;

<span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$patch</span> = <span class="hljs-keyword">new</span> Patch();

    <span class="hljs-variable">$value</span> = <span class="hljs-keyword">new</span> PayPalModel(<span class="hljs-string">'{
	       "state":"ACTIVE"
	     }'</span>);

    <span class="hljs-variable">$patch</span>-&gt;setOp(<span class="hljs-string">'replace'</span>)
        -&gt;setPath(<span class="hljs-string">'/'</span>)
        -&gt;setValue(<span class="hljs-variable">$value</span>);
    <span class="hljs-variable">$patchRequest</span> = <span class="hljs-keyword">new</span> PatchRequest();
    <span class="hljs-variable">$patchRequest</span>-&gt;addPatch(<span class="hljs-variable">$patch</span>);

    <span class="hljs-variable">$createdPlan</span>-&gt;update(<span class="hljs-variable">$patchRequest</span>, <span class="hljs-variable">$apiContext</span>);

    <span class="hljs-variable">$plan</span> = Plan::get(<span class="hljs-variable">$createdPlan</span>-&gt;getId(), <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Updated the Plan to Active State"</span>, <span class="hljs-string">"Plan"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$patchRequest</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Updated the Plan to Active State"</span>, <span class="hljs-string">"Plan"</span>, <span class="hljs-variable">$plan</span>-&gt;getId(), <span class="hljs-variable">$patchRequest</span>, <span class="hljs-variable">$plan</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$plan</span>;</div></div></div></div></body></html>
