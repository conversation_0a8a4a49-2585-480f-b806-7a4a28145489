<!DOCTYPE html><html lang="en"><head><title>invoice-templates/CreateInvoiceTemplate</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="invoice-templates/CreateInvoiceTemplate"><meta name="groc-project-path" content="invoice-templates/CreateInvoiceTemplate.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">invoice-templates/CreateInvoiceTemplate.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="create-invoice-template-sample">Create Invoice Template Sample</h1>
<p>This sample code demonstrate how you can create
an invoice template.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Currency</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">InvoiceItem</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">MerchantInfo</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Template</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">TemplateData</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">TemplateSettings</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">TemplateSettingsMetadata</span>;

<span class="hljs-keyword">require</span> <span class="hljs-keyword">__DIR__</span> . <span class="hljs-string">'/../bootstrap.php'</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="invoice-template-item">Invoice Template Item</h3></div></div></div><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-variable">$invoiceTemplateDataItem</span> = <span class="hljs-keyword">new</span> InvoiceItem();
<span class="hljs-variable">$invoiceTemplateDataItem</span>
    -&gt;setName(<span class="hljs-string">"Nutri Bullet"</span>)
    -&gt;setQuantity(<span class="hljs-number">1</span>)
    -&gt;setUnitPrice(<span class="hljs-keyword">new</span> Currency(<span class="hljs-string">'{ "currency": "USD", "value": "50.00" }'</span>));</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="invoice-template-data">Invoice Template Data</h3></div></div></div><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-variable">$invoiceTemplateData</span> = <span class="hljs-keyword">new</span> TemplateData();
<span class="hljs-variable">$invoiceTemplateData</span>
    -&gt;setTaxCalculatedAfterDiscount(<span class="hljs-keyword">false</span>)
    -&gt;setTaxInclusive(<span class="hljs-keyword">false</span>)
    -&gt;setNote(<span class="hljs-string">"Thank you for your business"</span>)
    -&gt;setLogoUrl(<span class="hljs-string">"https://pics.paypal.com/v1/images/redDot.jpeg"</span>)
    -&gt;addItem(<span class="hljs-variable">$invoiceTemplateDataItem</span>)
    -&gt;setMerchantInfo(<span class="hljs-keyword">new</span> MerchantInfo(<span class="hljs-string">'{ "email": "<EMAIL>" }'</span>));</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="template-settings">Template Settings</h3></div></div></div><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-variable">$displayPreferences</span> = <span class="hljs-keyword">new</span> TemplateSettingsMetadata();
<span class="hljs-variable">$displayPreferences</span>-&gt;setHidden(<span class="hljs-keyword">true</span>);

<span class="hljs-variable">$settingDate</span> = <span class="hljs-keyword">new</span> TemplateSettings();
<span class="hljs-variable">$settingDate</span>
    -&gt;setFieldName(<span class="hljs-string">"items.date"</span>)
    -&gt;setDisplayPreference(<span class="hljs-variable">$displayPreferences</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="template">Template</h3></div></div></div><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-variable">$invoiceTemplate</span> = <span class="hljs-keyword">new</span> Template();
<span class="hljs-variable">$invoiceTemplate</span>
    -&gt;setName(<span class="hljs-string">"Hours Template"</span> . rand())
    -&gt;setDefault(<span class="hljs-keyword">true</span>)
    -&gt;setUnitOfMeasure(<span class="hljs-string">"HOURS"</span>)
    -&gt;setTemplateData(<span class="hljs-variable">$invoiceTemplateData</span>)</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>This is another way of initializing the object.</p></div></div><div class="code"><div class="wrapper">    -&gt;addSetting(<span class="hljs-keyword">new</span> TemplateSettings(<span class="hljs-string">'{ "field_name": "custom", "display_preference": { "hidden": true } }'</span>))
    -&gt;addSetting(<span class="hljs-variable">$settingDate</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>For Sample Purposes Only.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$request</span> = <span class="hljs-keyword">clone</span> <span class="hljs-variable">$invoiceTemplate</span>;

<span class="hljs-keyword">try</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="create-invoice-template">Create Invoice Template</h3>
<p>Create an invoice by calling the invoice-&gt;create() method
with a valid ApiContext (See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$invoiceTemplate</span>-&gt;create(<span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Create Invoice Template"</span>, <span class="hljs-string">"Template"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$request</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">ResultPrinter::printResult(<span class="hljs-string">"Create Invoice Template"</span>, <span class="hljs-string">"Template"</span>, <span class="hljs-variable">$invoiceTemplate</span>-&gt;getTemplateId(), <span class="hljs-variable">$request</span>, <span class="hljs-variable">$invoiceTemplate</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$invoiceTemplate</span>;</div></div></div></div></body></html>
