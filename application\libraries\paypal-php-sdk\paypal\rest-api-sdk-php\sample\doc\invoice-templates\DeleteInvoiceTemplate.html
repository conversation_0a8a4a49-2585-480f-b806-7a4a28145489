<!DOCTYPE html><html lang="en"><head><title>invoice-templates/DeleteInvoiceTemplate</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="invoice-templates/DeleteInvoiceTemplate"><meta name="groc-project-path" content="invoice-templates/DeleteInvoiceTemplate.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">invoice-templates/DeleteInvoiceTemplate.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="delete-invoice-template-sample">Delete Invoice Template Sample</h1>
<p>This sample code demonstrate how you can delete
an invoice template</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> Template $template */</span>
<span class="hljs-variable">$template</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'CreateInvoiceTemplate.php'</span>;

<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Template</span>;

<span class="hljs-keyword">try</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="delete-invoice-template">Delete Invoice Template</h3>
<p>Delete invoice object by calling the
<code>delete</code> method
on the Invoice Template class by passing a valid
notification object
(See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$deleteStatus</span> = <span class="hljs-variable">$template</span>-&gt;delete(<span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Delete Invoice Template"</span>, <span class="hljs-string">"Template"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$deleteStatus</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Delete Invoice Template"</span>, <span class="hljs-string">"Template"</span>, <span class="hljs-variable">$template</span>-&gt;getTemplateId(), <span class="hljs-keyword">null</span>, <span class="hljs-keyword">null</span>);</div></div></div></div></body></html>
