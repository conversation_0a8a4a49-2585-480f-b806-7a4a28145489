<!DOCTYPE html><html lang="en"><head><title>invoice-templates/GetAllInvoiceTemplates</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="invoice-templates/GetAllInvoiceTemplates"><meta name="groc-project-path" content="invoice-templates/GetAllInvoiceTemplates.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">invoice-templates/GetAllInvoiceTemplates.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span>

<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Templates</span>;

<span class="hljs-keyword">require</span> <span class="hljs-string">'CreateInvoiceTemplate.php'</span>;

<span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$templates</span> = Templates::getAll(<span class="hljs-keyword">array</span>(<span class="hljs-string">"fields"</span> =&gt; <span class="hljs-string">"all"</span>), <span class="hljs-variable">$apiContext</span>);
}  <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Get all Templates"</span>, <span class="hljs-string">"Templates"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">ResultPrinter::printResult(<span class="hljs-string">"Get all Templates"</span>, <span class="hljs-string">"Templates"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$templates</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$templates</span>;</div></div></div></div></body></html>
