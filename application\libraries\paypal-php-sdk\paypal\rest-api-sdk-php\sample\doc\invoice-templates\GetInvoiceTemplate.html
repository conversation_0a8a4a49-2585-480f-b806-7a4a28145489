<!DOCTYPE html><html lang="en"><head><title>invoice-templates/GetInvoiceTemplate</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="invoice-templates/GetInvoiceTemplate"><meta name="groc-project-path" content="invoice-templates/GetInvoiceTemplate.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">invoice-templates/GetInvoiceTemplate.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="retrieve-invoice-template-sample">Retrieve Invoice Template Sample</h1>
<p>This sample code demonstrate how you can get
an invoice template using templateId.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Template</span>;

<span class="hljs-variable">$invoiceTemplate</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'CreateInvoiceTemplate.php'</span>;

<span class="hljs-comment">/** <span class="hljs-doctag">@var</span> Template $invoiceTemplate */</span>
<span class="hljs-variable">$templateId</span> = <span class="hljs-variable">$invoiceTemplate</span>-&gt;getTemplateId();</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="retrieve-invoice-template">Retrieve Invoice Template</h3>
<p>Retrieve the invoice template object by calling the
static <code>get</code> method
on the Template class by passing a valid
Template ID
(See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$template</span> = Template::get(<span class="hljs-variable">$templateId</span>, <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Get Invoice Template"</span>, <span class="hljs-string">"Template"</span>, <span class="hljs-variable">$template</span>-&gt;getTemplateId(), <span class="hljs-variable">$templateId</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">ResultPrinter::printResult(<span class="hljs-string">"Get Invoice Template"</span>, <span class="hljs-string">"Template"</span>, <span class="hljs-variable">$template</span>-&gt;getTemplateId(), <span class="hljs-variable">$templateId</span>, <span class="hljs-variable">$template</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$template</span>;</div></div></div></div></body></html>
