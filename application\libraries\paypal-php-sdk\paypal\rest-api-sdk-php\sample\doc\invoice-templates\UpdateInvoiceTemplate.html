<!DOCTYPE html><html lang="en"><head><title>invoice-templates/UpdateInvoiceTemplate</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="invoice-templates/UpdateInvoiceTemplate"><meta name="groc-project-path" content="invoice-templates/UpdateInvoiceTemplate.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">invoice-templates/UpdateInvoiceTemplate.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="update-invoice-sample">Update Invoice Sample</h1>
<p>This sample code demonstrate how you can update
an invoice.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> Template $template */</span>
<span class="hljs-variable">$template</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'GetInvoiceTemplate.php'</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Template</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="update-invoice">Update Invoice</h3></div></div></div><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-variable">$template</span>-&gt;setUnitOfMeasure(<span class="hljs-string">"QUANTITY"</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="note-these-are-the-work-around-added-to-the">NOTE: These are the work-around added to the</h3>
<p>sample, to get past the bug in PayPal APIs.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$template</span>-&gt;setCustom(<span class="hljs-keyword">null</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>For Sample Purposes Only.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$request</span> = <span class="hljs-keyword">clone</span> <span class="hljs-variable">$template</span>;
<span class="hljs-keyword">try</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="update-invoice-template">Update Invoice Template</h3>
<p>Update an invoice by calling the invoice-&gt;update() method
with a valid ApiContext (See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$template</span>-&gt;update(<span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Invoice Template Updated"</span>, <span class="hljs-string">"Invoice"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$request</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Invoice Template Updated"</span>, <span class="hljs-string">"Invoice"</span>, <span class="hljs-variable">$template</span>-&gt;getTemplateId(), <span class="hljs-variable">$request</span>, <span class="hljs-variable">$template</span>);</div></div></div></div></body></html>
