<!DOCTYPE html><html lang="en"><head><title>invoice/CancelInvoice</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="invoice/CancelInvoice"><meta name="groc-project-path" content="invoice/CancelInvoice.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">invoice/CancelInvoice.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="cancel-invoice-sample">Cancel Invoice Sample</h1>
<p>This sample code demonstrate how you can cancel
an invoice.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> Invoice $invoice */</span>
<span class="hljs-variable">$invoice</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'SendInvoice.php'</span>;

<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">CancelNotification</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Invoice</span>;

<span class="hljs-keyword">try</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="cancel-notification-object">Cancel Notification Object</h3>
<p>This would send a notification to both merchant as well
the payer about the cancellation. The information of
merchant and payer is retrieved from the invoice details</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$notify</span> = <span class="hljs-keyword">new</span> CancelNotification();
    <span class="hljs-variable">$notify</span>
        -&gt;setSubject(<span class="hljs-string">"Past due"</span>)
        -&gt;setNote(<span class="hljs-string">"Canceling invoice"</span>)
        -&gt;setSendToMerchant(<span class="hljs-keyword">true</span>)
        -&gt;setSendToPayer(<span class="hljs-keyword">true</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="cancel-invoice">Cancel Invoice</h3>
<p>Cancel invoice object by calling the
static <code>cancel</code> method
on the Invoice class by passing a valid
notification object
(See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$cancelStatus</span> = <span class="hljs-variable">$invoice</span>-&gt;cancel(<span class="hljs-variable">$notify</span>, <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Cancel Invoice"</span>, <span class="hljs-string">"Invoice"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$notify</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Cancel Invoice"</span>, <span class="hljs-string">"Invoice"</span>, <span class="hljs-variable">$invoice</span>-&gt;getId(), <span class="hljs-variable">$notify</span>, <span class="hljs-keyword">null</span>);</div></div></div></div></body></html>
