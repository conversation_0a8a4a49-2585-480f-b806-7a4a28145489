<!DOCTYPE html><html lang="en"><head><title>invoice/CreateThirdPartyInvoice</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="invoice/CreateThirdPartyInvoice"><meta name="groc-project-path" content="invoice/CreateThirdPartyInvoice.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">invoice/CreateThirdPartyInvoice.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="create-third-party-invoice-sample">Create Third Party Invoice Sample</h1>
<p>This sample code demonstrate how you can create third party invoice on someone else&#39;s behalf.
This requires using <code>Obtain User&#39;s Consent</code> to fetch the refresh token of the third party merchant. Please look at <a href="http://paypal.github.io/PayPal-PHP-SDK/sample/doc/lipp/ObtainUserConsent.html">http://paypal.github.io/PayPal-PHP-SDK/sample/doc/lipp/ObtainUserConsent.html</a> for more info.
You need the email address of the third party. This can be retrieved using the refresh token obtained from previous call. Please refere to <a href="http://paypal.github.io/PayPal-PHP-SDK/sample/doc/lipp/GetUserInfo.html">http://paypal.github.io/PayPal-PHP-SDK/sample/doc/lipp/GetUserInfo.html</a> for more info.
Please make sure to use <code>merchantInfo.email</code> as the email address of the third party.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">require</span> <span class="hljs-keyword">__DIR__</span> . <span class="hljs-string">'/../bootstrap.php'</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">BillingInfo</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Currency</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Invoice</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">InvoiceAddress</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">InvoiceItem</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">MerchantInfo</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">PaymentTerm</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">ShippingInfo</span>;

<span class="hljs-variable">$invoice</span> = <span class="hljs-keyword">new</span> Invoice();</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="invoice-info">Invoice Info</h3>
<p>Fill in all the information that is
required for invoice APIs</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$invoice</span>
    -&gt;setMerchantInfo(<span class="hljs-keyword">new</span> MerchantInfo())
    -&gt;setBillingInfo(<span class="hljs-keyword">array</span>(<span class="hljs-keyword">new</span> BillingInfo()))
    -&gt;setNote(<span class="hljs-string">"Medical Invoice 16 Jul, 2013 PST"</span>)
    -&gt;setPaymentTerm(<span class="hljs-keyword">new</span> PaymentTerm());</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="merchant-info">Merchant Info</h3>
<p>A resource representing merchant information that can be
used to identify merchant</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$invoice</span>-&gt;getMerchantInfo()</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>This would be the email address of third party merchant.</p></div></div><div class="code"><div class="wrapper">    -&gt;setEmail(<span class="hljs-string">"<EMAIL>"</span>)
    -&gt;setFirstName(<span class="hljs-string">"Dennis"</span>)
    -&gt;setLastName(<span class="hljs-string">"Doctor"</span>)
    -&gt;setbusinessName(<span class="hljs-string">"Medical Professionals, LLC"</span>)
    -&gt;setAddress(<span class="hljs-keyword">new</span> InvoiceAddress());</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="address-information">Address Information</h3>
<p>The address used for creating the invoice</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$invoice</span>-&gt;getMerchantInfo()-&gt;getAddress()
    -&gt;setLine1(<span class="hljs-string">"1234 Main St."</span>)
    -&gt;setCity(<span class="hljs-string">"Portland"</span>)
    -&gt;setState(<span class="hljs-string">"OR"</span>)
    -&gt;setPostalCode(<span class="hljs-string">"97217"</span>)
    -&gt;setCountryCode(<span class="hljs-string">"US"</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="billing-information">Billing Information</h3>
<p>Set the email address for each billing</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$billing</span> = <span class="hljs-variable">$invoice</span>-&gt;getBillingInfo();
<span class="hljs-variable">$billing</span>[<span class="hljs-number">0</span>]
    -&gt;setEmail(<span class="hljs-string">"<EMAIL>"</span>);

<span class="hljs-variable">$billing</span>[<span class="hljs-number">0</span>]-&gt;setBusinessName(<span class="hljs-string">"Jay Inc"</span>)
    -&gt;setAdditionalInfo(<span class="hljs-string">"This is the billing Info"</span>)
    -&gt;setAddress(<span class="hljs-keyword">new</span> InvoiceAddress());

<span class="hljs-variable">$billing</span>[<span class="hljs-number">0</span>]-&gt;getAddress()
    -&gt;setLine1(<span class="hljs-string">"1234 Main St."</span>)
    -&gt;setCity(<span class="hljs-string">"Portland"</span>)
    -&gt;setState(<span class="hljs-string">"OR"</span>)
    -&gt;setPostalCode(<span class="hljs-string">"97217"</span>)
    -&gt;setCountryCode(<span class="hljs-string">"US"</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="items-list">Items List</h3>
<p>You could provide the list of all items for
detailed breakdown of invoice</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$items</span> = <span class="hljs-keyword">array</span>();
<span class="hljs-variable">$items</span>[<span class="hljs-number">0</span>] = <span class="hljs-keyword">new</span> InvoiceItem();
<span class="hljs-variable">$items</span>[<span class="hljs-number">0</span>]
    -&gt;setName(<span class="hljs-string">"Sutures"</span>)
    -&gt;setQuantity(<span class="hljs-number">100</span>)
    -&gt;setUnitPrice(<span class="hljs-keyword">new</span> Currency());

<span class="hljs-variable">$items</span>[<span class="hljs-number">0</span>]-&gt;getUnitPrice()
    -&gt;setCurrency(<span class="hljs-string">"USD"</span>)
    -&gt;setValue(<span class="hljs-number">5</span>);

<span class="hljs-variable">$invoice</span>-&gt;getPaymentTerm()
    -&gt;setTermType(<span class="hljs-string">"NET_45"</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>For Sample Purposes Only.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$request</span> = <span class="hljs-keyword">clone</span> <span class="hljs-variable">$invoice</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>This would be refresh token retrieved from <a href="http://paypal.github.io/PayPal-PHP-SDK/sample/doc/lipp/ObtainUserConsent.html">http://paypal.github.io/PayPal-PHP-SDK/sample/doc/lipp/ObtainUserConsent.html</a></p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$refreshToken</span> = <span class="hljs-string">"SCNWVZfdg43XaOmoEicazpkXyda32CGnP208EkuQ_QBIrXCYMhlvORFHHyoXPT0VbEMIHYVJEm0gVf1Vf72YgJzPScBenKoVPq__y1QRT7wwJo3WYADwUW4Q5ic"</span>;

<span class="hljs-keyword">try</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="use-refresh-token-make-sure-to-update-merchantinfo-email-based-on">Use Refresh Token. MAKE SURE TO update <code>MerchantInfo.Email</code> based on</h3></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$invoice</span>-&gt;updateAccessToken(<span class="hljs-variable">$refreshToken</span>, <span class="hljs-variable">$apiContext</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="create-invoice">Create Invoice</h3>
<p>Create an invoice by calling the invoice-&gt;create() method
with a valid ApiContext (See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$invoice</span>-&gt;create(<span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Create Invoice"</span>, <span class="hljs-string">"Invoice"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$request</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Create Invoice"</span>, <span class="hljs-string">"Invoice"</span>, <span class="hljs-variable">$invoice</span>-&gt;getId(), <span class="hljs-variable">$request</span>, <span class="hljs-variable">$invoice</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="send-invoice">Send Invoice</h3></div></div></div><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="send-invoice">Send Invoice</h3></div></div></div><div class="segment"><div class="code"><div class="wrapper">    <span class="hljs-variable">$invoice</span>-&gt;send(<span class="hljs-variable">$apiContext</span>);
    <span class="hljs-variable">$invoice</span> = Invoice::get(<span class="hljs-variable">$invoice</span>-&gt;getId(), <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Send Invoice"</span>, <span class="hljs-string">"Invoice"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$request</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">ResultPrinter::printResult(<span class="hljs-string">"Send Invoice"</span>, <span class="hljs-string">"Invoice"</span>, <span class="hljs-variable">$invoice</span>-&gt;getId(), <span class="hljs-variable">$request</span>, <span class="hljs-variable">$invoice</span>);


<span class="hljs-keyword">return</span> <span class="hljs-variable">$invoice</span>;</div></div></div></div></body></html>
