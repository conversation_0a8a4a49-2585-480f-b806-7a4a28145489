<!DOCTYPE html><html lang="en"><head><title>invoice/GetNextInvoiceNumber</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="invoice/GetNextInvoiceNumber"><meta name="groc-project-path" content="invoice/GetNextInvoiceNumber.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">invoice/GetNextInvoiceNumber.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="get-next-invoice-number-sample">Get Next Invoice Number Sample</h1>
<p>This sample code demonstrate how you can retrieve
the next invoice number.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">require</span> <span class="hljs-string">'../bootstrap.php'</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Invoice</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="get-next-invoice-number">Get Next Invoice Number</h3>
<p>To generate the successive invoice number for the merchant, use below code.
(See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$number</span> = Invoice::generateNumber(<span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Get Next Invoice Number"</span>, <span class="hljs-string">"InvoiceNumber"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Get Next Invoice Number"</span>, <span class="hljs-string">"InvoiceNumber"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$number</span>, <span class="hljs-variable">$number</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$number</span>;</div></div></div></div></body></html>
