<!DOCTYPE html><html lang="en"><head><title>invoice/RecordRefund</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="invoice/RecordRefund"><meta name="groc-project-path" content="invoice/RecordRefund.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">invoice/RecordRefund.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="record-refund-sample">Record Refund Sample</h1>
<p>This sample code demonstrate how you can record
an invoice as refunded.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> Invoice $invoice */</span>
<span class="hljs-variable">$invoice</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'RecordPayment.php'</span>;

<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Invoice</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">RefundDetail</span>;

<span class="hljs-keyword">try</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="record-object">Record Object</h3>
<p>Create a RefundDetail object, and fill in the required fields
You can use the new way of injecting json directly to the object.</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$refund</span> = <span class="hljs-keyword">new</span> RefundDetail(
        <span class="hljs-string">'{
          "date" : "2014-07-06 03:30:00 PST",
          "note" : "Refund provided by cash."
        }'</span>
    );</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="record-refund-for-invoice">Record Refund for Invoice</h3>
<p>Record a refund on invoice object by calling the
<code>recordRefund</code> method
on the Invoice class by passing a valid
notification object
(See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$refundStatus</span> = <span class="hljs-variable">$invoice</span>-&gt;recordRefund(<span class="hljs-variable">$refund</span>, <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Refund for Invoice"</span>, <span class="hljs-string">"Invoice"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Refund for Invoice"</span>, <span class="hljs-string">"Invoice"</span>, <span class="hljs-variable">$invoice</span>-&gt;getId(), <span class="hljs-variable">$refund</span>, <span class="hljs-keyword">null</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="retrieve-invoice">Retrieve Invoice</h3>
<p>Retrieve the invoice object by calling the
static <code>get</code> method
on the Invoice class by passing a valid
Invoice ID
(See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$invoice</span> = Invoice::get(<span class="hljs-variable">$invoice</span>-&gt;getId(), <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Get Invoice (Not Required - For Sample Only)"</span>, <span class="hljs-string">"Invoice"</span>, <span class="hljs-variable">$invoice</span>-&gt;getId(), <span class="hljs-variable">$invoice</span>-&gt;getId(), <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Get Invoice (Not Required - For Sample Only)"</span>, <span class="hljs-string">"Invoice"</span>, <span class="hljs-variable">$invoice</span>-&gt;getId(), <span class="hljs-variable">$invoice</span>-&gt;getId(), <span class="hljs-variable">$invoice</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$invoice</span>;</div></div></div></div></body></html>
