<!DOCTYPE html><html lang="en"><head><title>invoice/RemindInvoice</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="invoice/RemindInvoice"><meta name="groc-project-path" content="invoice/RemindInvoice.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">invoice/RemindInvoice.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="remind-invoice-sample">Remind Invoice Sample</h1>
<p>This sample code demonstrate how you can remind
an invoice to the payer</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> Invoice $invoice */</span>
<span class="hljs-variable">$invoice</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'SendInvoice.php'</span>;

<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Invoice</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Notification</span>;

<span class="hljs-keyword">try</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="notification-object">Notification Object</h3>
<p>This would send a notification to both merchant as well
the payer. The information of merchant
and payer is retrieved from the invoice details</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$notify</span> = <span class="hljs-keyword">new</span> Notification();
    <span class="hljs-variable">$notify</span>
        -&gt;setSubject(<span class="hljs-string">"Past due"</span>)
        -&gt;setNote(<span class="hljs-string">"Please pay soon"</span>)
        -&gt;setSendToMerchant(<span class="hljs-keyword">true</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="remind-invoice">Remind Invoice</h3>
<p>Remind the notifiers by calling the
<code>remind</code> method
on the Invoice class by passing a valid
notification object
(See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$remindStatus</span> = <span class="hljs-variable">$invoice</span>-&gt;remind(<span class="hljs-variable">$notify</span>, <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Remind Invoice"</span>, <span class="hljs-string">"Invoice"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$notify</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Remind Invoice"</span>, <span class="hljs-string">"Invoice"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$notify</span>, <span class="hljs-keyword">null</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="retrieve-invoice">Retrieve Invoice</h3>
<p>Retrieve the invoice object by calling the
static <code>get</code> method
on the Invoice class by passing a valid
Invoice ID
(See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$invoice</span> = Invoice::get(<span class="hljs-variable">$invoice</span>-&gt;getId(), <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Get Invoice (Not Required - For Sample Only)"</span>, <span class="hljs-string">"Invoice"</span>, <span class="hljs-variable">$invoice</span>-&gt;getId(), <span class="hljs-variable">$invoice</span>-&gt;getId(), <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Get Invoice (Not Required - For Sample Only)"</span>, <span class="hljs-string">"Invoice"</span>, <span class="hljs-variable">$invoice</span>-&gt;getId(), <span class="hljs-variable">$invoice</span>-&gt;getId(), <span class="hljs-variable">$invoice</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$invoice</span>;</div></div></div></div></body></html>
