<!DOCTYPE html><html lang="en"><head><title>invoice/RetrieveQRCode</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="invoice/RetrieveQRCode"><meta name="groc-project-path" content="invoice/RetrieveQRCode.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">invoice/RetrieveQRCode.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="retrieve-qr-code-for-invoice-sample">Retrieve QR Code for Invoice Sample</h1>
<p>Specify an invoice ID to get a QR code (image) that corresponds to the invoice ID. A QR code for an invoice can be added to a paper or PDF invoice. When a customer uses their mobile device to scan the QR code, the customer is redirected to the PayPal mobile payment flow, where they can pay online with PayPal or a credit card.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> Invoice $invoice */</span>
<span class="hljs-variable">$invoice</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'SendInvoice.php'</span>;

<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Invoice</span>;

<span class="hljs-keyword">try</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="retrieve-qr-code-of-sent-invoice">Retrieve QR Code of Sent Invoice</h3>
<p>Retrieve QR Code of Sent Invoice by calling the
<code>qrCode</code> method
on the Invoice class by passing a valid
notification object
(See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$image</span> = Invoice::qrCode(<span class="hljs-variable">$invoice</span>-&gt;getId(), <span class="hljs-keyword">array</span>(<span class="hljs-string">'height'</span> =&gt; <span class="hljs-string">'300'</span>, <span class="hljs-string">'width'</span> =&gt; <span class="hljs-string">'300'</span>), <span class="hljs-variable">$apiContext</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="optionally-save-to-file">Optionally Save to File</h3>
<p>This is not a required step. However, if you want to store this image as a file, you can use
&#39;saveToFile&#39; method with proper file name.
This will save the image as /samples/invoice/images/sample.png</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$path</span> = <span class="hljs-variable">$image</span>-&gt;saveToFile(<span class="hljs-string">"images/sample.png"</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Retrieved QR Code for Invoice"</span>, <span class="hljs-string">"Invoice"</span>, <span class="hljs-variable">$invoice</span>-&gt;getId(), <span class="hljs-keyword">null</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Retrieved QR Code for Invoice"</span>, <span class="hljs-string">"Invoice"</span>, <span class="hljs-variable">$invoice</span>-&gt;getId(), <span class="hljs-keyword">null</span>, <span class="hljs-variable">$image</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="show-the-image">Show the Image</h3>
<p>In PHP, there are many ways to present an images.
One of the ways, you could directly inject the base64-encoded string
with proper image information in front of it.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">echo</span> <span class="hljs-string">'&lt;img src="data:image/png;base64,'</span>. <span class="hljs-variable">$image</span>-&gt;getImage() . <span class="hljs-string">'" alt="Invoice QR Code" /&gt;'</span>;</div></div></div></div></body></html>
