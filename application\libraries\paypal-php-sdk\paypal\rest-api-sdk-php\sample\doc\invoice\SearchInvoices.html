<!DOCTYPE html><html lang="en"><head><title>invoice/SearchInvoices</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="invoice/SearchInvoices"><meta name="groc-project-path" content="invoice/SearchInvoices.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">invoice/SearchInvoices.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="search-invoices-sample">Search Invoices Sample</h1>
<p>This sample code demonstrate how you can
search invoices from history.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> Invoice $invoice */</span>
<span class="hljs-variable">$invoice</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'CreateInvoice.php'</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Invoice</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Search</span>;

<span class="hljs-keyword">try</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="search-object">Search Object</h3>
<p>Fill up your search criteria for Invoice search.
Using the new way to inject raw json string to constructor</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$search</span> = <span class="hljs-keyword">new</span> Search(
        <span class="hljs-string">'{
          "start_invoice_date" : "2010-05-10 PST",
          "end_invoice_date" : "2019-05-11 PST",
          "page" : 1,
          "page_size" : 20,
          "total_count_required" : true
        }'</span>
    );</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="search-invoices">Search Invoices</h3>
<p>Retrieve the Invoice History object by calling the
static <code>search</code> method on the Invoice class.
Refer the method doc for valid values for keys
(See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$invoices</span> = Invoice::search(<span class="hljs-variable">$search</span>, <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Search Invoice"</span>, <span class="hljs-string">"Invoice"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Search Invoice"</span>, <span class="hljs-string">"Invoice"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$search</span>, <span class="hljs-variable">$invoices</span>);</div></div></div></div></body></html>
