<!DOCTYPE html><html lang="en"><head><title>invoice/UpdateInvoice</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="invoice/UpdateInvoice"><meta name="groc-project-path" content="invoice/UpdateInvoice.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">invoice/UpdateInvoice.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="update-invoice-sample">Update Invoice Sample</h1>
<p>This sample code demonstrate how you can update
an invoice.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> Invoice $invoice */</span>
<span class="hljs-variable">$invoice</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'CreateInvoice.php'</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Invoice</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>For Sample Purposes Only.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$request</span> = <span class="hljs-keyword">clone</span> <span class="hljs-variable">$invoice</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="update-invoice">Update Invoice</h3>
<p>Lets update some information</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$invoice</span>-&gt;setInvoiceDate(<span class="hljs-string">"2014-12-16 PST"</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="note-these-are-the-work-around-added-to-the">NOTE: These are the work-around added to the</h3>
<p>sample, to get past the bug in PayPal APIs.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$invoice</span>-&gt;setDiscount(<span class="hljs-keyword">null</span>);
<span class="hljs-variable">$invoice</span>-&gt;getItems()[<span class="hljs-number">1</span>]-&gt;setDiscount(<span class="hljs-keyword">null</span>);

<span class="hljs-keyword">try</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="update-invoice">Update Invoice</h3>
<p>Update an invoice by calling the invoice-&gt;update() method
with a valid ApiContext (See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$invoice</span>-&gt;update(<span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Invoice Updated"</span>, <span class="hljs-string">"Invoice"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$request</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Invoice Updated"</span>, <span class="hljs-string">"Invoice"</span>, <span class="hljs-variable">$invoice</span>-&gt;getId(), <span class="hljs-variable">$request</span>, <span class="hljs-variable">$invoice</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="retrieve-invoice">Retrieve Invoice</h3>
<p>Retrieve the invoice object by calling the
static <code>get</code> method
on the Invoice class by passing a valid
Invoice ID
(See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$invoice</span> = Invoice::get(<span class="hljs-variable">$invoice</span>-&gt;getId(), <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Get Invoice (Not Required - For Sample Only)"</span>, <span class="hljs-string">"Invoice"</span>, <span class="hljs-variable">$invoice</span>-&gt;getId(), <span class="hljs-variable">$invoice</span>-&gt;getId(), <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Get Invoice (Not Required - For Sample Only)"</span>, <span class="hljs-string">"Invoice"</span>, <span class="hljs-variable">$invoice</span>-&gt;getId(), <span class="hljs-variable">$invoice</span>-&gt;getId(), <span class="hljs-variable">$invoice</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$invoice</span>;</div></div></div></div></body></html>
