<!DOCTYPE html><html lang="en"><head><title>lipp/GenerateAccessTokenFromRefreshToken</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="lipp/GenerateAccessTokenFromRefreshToken"><meta name="groc-project-path" content="lipp/GenerateAccessTokenFromRefreshToken.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">lipp/GenerateAccessTokenFromRefreshToken.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="obtain-access-token-from-refresh-token">Obtain Access Token From Refresh Token</h3></div></div></div><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-keyword">require</span> <span class="hljs-keyword">__DIR__</span> . <span class="hljs-string">'/../bootstrap.php'</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">OpenIdTokeninfo</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>You can retrieve the refresh token by executing ObtainUserConsent.php and store the refresh token</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$refreshToken</span> = <span class="hljs-string">'yzX4AkmMyBKR4on7vB5he-tDu38s24Zy-kTibhSuqA8kTdy0Yinxj7NpAyULx0bxqC5G8dbXOt0aVMlMmtpiVmSzhcjVZhYDM7WUQLC9KpaXGBHyltJPkLLQkXE'</span>;

<span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$tokenInfo</span> = <span class="hljs-keyword">new</span> OpenIdTokeninfo();
    <span class="hljs-variable">$tokenInfo</span> = <span class="hljs-variable">$tokenInfo</span>-&gt;createFromRefreshToken(<span class="hljs-keyword">array</span>(<span class="hljs-string">'refresh_token'</span> =&gt; <span class="hljs-variable">$refreshToken</span>), <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Obtained Access Token From Refresh Token"</span>, <span class="hljs-string">"Access Token"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Obtained Access Token From Refresh Token"</span>, <span class="hljs-string">"Access Token"</span>, <span class="hljs-variable">$tokenInfo</span>-&gt;getAccessToken(), <span class="hljs-keyword">null</span>, <span class="hljs-variable">$tokenInfo</span>);</div></div></div></div></body></html>
