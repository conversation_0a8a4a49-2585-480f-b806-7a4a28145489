<!DOCTYPE html><html lang="en"><head><title>lipp/GetUserInfo</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="lipp/GetUserInfo"><meta name="groc-project-path" content="lipp/GetUserInfo.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">lipp/GetUserInfo.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="obtain-access-token-from-refresh-token">Obtain Access Token From Refresh Token</h3></div></div></div><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-keyword">require</span> <span class="hljs-keyword">__DIR__</span> . <span class="hljs-string">'/../bootstrap.php'</span>;

<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">OpenIdTokeninfo</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">OpenIdUserinfo</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>To obtain User Info, you have to follow three steps in general.
First, you need to obtain user&#39;s consent to retrieve the information you want.
This is explained in the example &quot;ObtainUserConsent.php&quot;.</p></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Once you get the user&#39;s consent, the end result would be long lived refresh token.
This refresh token should be stored in a permanent storage for later use.</p></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Lastly, when you need to retrieve the user information, you need to generate the short lived access token
to retreive the information. The short lived access token can be retrieved using the example shown in
&quot;GenerateAccessTokenFromRefreshToken.php&quot;, or as shown below</p></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>You can retrieve the refresh token by executing ObtainUserConsent.php and store the refresh token</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$refreshToken</span> = <span class="hljs-string">'W1JmxG-Cogm-4aSc5Vlen37XaQTj74aQcQiTtXax5UgY7M_AJ--kLX8xNVk8LtCpmueFfcYlRK6UgQLJ-XHsxpw6kZzPpKKccRQeC4z2ldTMfXdIWajZ6CHuebs'</span>;

<span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$tokenInfo</span> = <span class="hljs-keyword">new</span> OpenIdTokeninfo();
    <span class="hljs-variable">$tokenInfo</span> = <span class="hljs-variable">$tokenInfo</span>-&gt;createFromRefreshToken(<span class="hljs-keyword">array</span>(<span class="hljs-string">'refresh_token'</span> =&gt; <span class="hljs-variable">$refreshToken</span>), <span class="hljs-variable">$apiContext</span>);

    <span class="hljs-variable">$params</span> = <span class="hljs-keyword">array</span>(<span class="hljs-string">'access_token'</span> =&gt; <span class="hljs-variable">$tokenInfo</span>-&gt;getAccessToken());
    <span class="hljs-variable">$userInfo</span> = OpenIdUserinfo::getUserinfo(<span class="hljs-variable">$params</span>, <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"User Information"</span>, <span class="hljs-string">"User Info"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$params</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"User Information"</span>, <span class="hljs-string">"User Info"</span>, <span class="hljs-variable">$userInfo</span>-&gt;getUserId(), <span class="hljs-variable">$params</span>, <span class="hljs-variable">$userInfo</span>);</div></div></div></div></body></html>
