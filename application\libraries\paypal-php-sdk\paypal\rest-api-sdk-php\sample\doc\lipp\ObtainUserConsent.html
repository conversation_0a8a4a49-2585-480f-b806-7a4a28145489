<!DOCTYPE html><html lang="en"><head><title>lipp/ObtainUserConsent</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="lipp/ObtainUserConsent"><meta name="groc-project-path" content="lipp/ObtainUserConsent.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">lipp/ObtainUserConsent.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span>

<span class="hljs-keyword">require</span> <span class="hljs-keyword">__DIR__</span> . <span class="hljs-string">'/../bootstrap.php'</span>;

<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">OpenIdSession</span>;

<span class="hljs-variable">$baseUrl</span> = getBaseUrl() . <span class="hljs-string">'/UserConsentRedirect.php?success=true'</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="get-user-consent-url">Get User Consent URL</h3>
<p>The clientId is stored in the bootstrap file</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">//Get Authorization URL returns the redirect URL that could be used to get user's consent</span>
<span class="hljs-variable">$redirectUrl</span> = OpenIdSession::getAuthorizationUrl(
    <span class="hljs-variable">$baseUrl</span>,
    <span class="hljs-keyword">array</span>(<span class="hljs-string">'openid'</span>, <span class="hljs-string">'profile'</span>, <span class="hljs-string">'address'</span>, <span class="hljs-string">'email'</span>, <span class="hljs-string">'phone'</span>,
        <span class="hljs-string">'https://uri.paypal.com/services/paypalattributes'</span>,
        <span class="hljs-string">'https://uri.paypal.com/services/expresscheckout'</span>,
        <span class="hljs-string">'https://uri.paypal.com/services/invoicing'</span>),
    <span class="hljs-keyword">null</span>,
    <span class="hljs-keyword">null</span>,
    <span class="hljs-keyword">null</span>,
    <span class="hljs-variable">$apiContext</span>
);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Generated the User Consent URL"</span>, <span class="hljs-string">"URL"</span>, <span class="hljs-string">'&lt;a href="'</span>. <span class="hljs-variable">$redirectUrl</span> . <span class="hljs-string">'" &gt;Click Here to Obtain User Consent&lt;/a&gt;'</span>, <span class="hljs-variable">$baseUrl</span>, <span class="hljs-variable">$redirectUrl</span>);</div></div></div></div></body></html>
