<!DOCTYPE html><html lang="en"><head><title>lipp/UserConsentRedirect</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="lipp/UserConsentRedirect"><meta name="groc-project-path" content="lipp/UserConsentRedirect.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">lipp/UserConsentRedirect.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span>

<span class="hljs-keyword">require</span> <span class="hljs-keyword">__DIR__</span> . <span class="hljs-string">'/../bootstrap.php'</span>;

<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">OpenIdTokeninfo</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Exception</span>\<span class="hljs-title">PayPalConnectionException</span>;

session_start();</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="user-consent-response">User Consent Response</h3>
<p>PayPal would redirect the user to the redirect_uri mentioned when creating the consent URL.
The user would then able to retrieve the access token by getting the code, which is returned as a GET parameter.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">if</span> (<span class="hljs-keyword">isset</span>(<span class="hljs-variable">$_GET</span>[<span class="hljs-string">'success'</span>]) &amp;&amp; <span class="hljs-variable">$_GET</span>[<span class="hljs-string">'success'</span>] == <span class="hljs-string">'true'</span>) {
    <span class="hljs-variable">$code</span> = <span class="hljs-variable">$_GET</span>[<span class="hljs-string">'code'</span>];

    <span class="hljs-keyword">try</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Obtain Authorization Code from Code, Client ID and Client Secret</p></div></div><div class="code"><div class="wrapper">        <span class="hljs-variable">$accessToken</span> = OpenIdTokeninfo::createFromAuthorizationCode(<span class="hljs-keyword">array</span>(<span class="hljs-string">'code'</span> =&gt; <span class="hljs-variable">$code</span>), <span class="hljs-keyword">null</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$apiContext</span>);
    } <span class="hljs-keyword">catch</span> (PayPalConnectionException <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Obtained Access Token"</span>, <span class="hljs-string">"Access Token"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$_GET</span>[<span class="hljs-string">'code'</span>], <span class="hljs-variable">$ex</span>);
        <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
    }</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Obtained Access Token"</span>, <span class="hljs-string">"Access Token"</span>, <span class="hljs-variable">$accessToken</span>-&gt;getAccessToken(), <span class="hljs-variable">$_GET</span>[<span class="hljs-string">'code'</span>], <span class="hljs-variable">$accessToken</span>);
}</div></div></div></div></body></html>
