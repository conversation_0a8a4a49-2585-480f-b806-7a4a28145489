<!DOCTYPE html><html lang="en"><head><title>notifications/GetWebhook</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="notifications/GetWebhook"><meta name="groc-project-path" content="notifications/GetWebhook.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">notifications/GetWebhook.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="get-webhook-sample">Get Webhook Sample</h1>
<p>This sample code demonstrate how you can get a webhook, as documented here at:
<a href="https://developer.paypal.com/webapps/developer/docs/api/#get-a-webhook">https://developer.paypal.com/webapps/developer/docs/api/#get-a-webhook</a>
API used: GET /v1/notifications/webhooks/<Webhook-Id></p></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h2 id="get-webhook-id">Get Webhook ID.</h2>
<p>In samples we are using CreateWebhook.php sample to get the created instance of webhook.
However, in real case scenario, we could use just the ID from database or retrieved from the form.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> \PayPal\Api\Webhook $webhook */</span>
<span class="hljs-variable">$webhook</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'CreateWebhook.php'</span>;
<span class="hljs-variable">$webhookId</span> = <span class="hljs-variable">$webhook</span>-&gt;getId();</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="get-webhook">Get Webhook</h3></div></div></div><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$output</span> = \PayPal\Api\Webhook::get(<span class="hljs-variable">$webhookId</span>, <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Get a Webhook"</span>, <span class="hljs-string">"Webhook"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$webhookId</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Get a Webhook"</span>, <span class="hljs-string">"Webhook"</span>, <span class="hljs-variable">$output</span>-&gt;getId(), <span class="hljs-keyword">null</span>, <span class="hljs-variable">$output</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$output</span>;</div></div></div></div></body></html>
