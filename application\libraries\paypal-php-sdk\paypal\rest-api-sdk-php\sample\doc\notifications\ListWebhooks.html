<!DOCTYPE html><html lang="en"><head><title>notifications/ListWebhooks</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="notifications/ListWebhooks"><meta name="groc-project-path" content="notifications/ListWebhooks.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">notifications/ListWebhooks.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="get-all-webhooks-sample">Get All Webhooks Sample</h1>
<p>Use this call to list all the webhooks, as documented here at:
<a href="https://developer.paypal.com/webapps/developer/docs/api/#list-all-webhooks">https://developer.paypal.com/webapps/developer/docs/api/#list-all-webhooks</a>
API used: GET /v1/notifications/webhooks</p></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h2 id="list-webhooks">List Webhooks</h2></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>This step is not necessarily required. We are creating a webhook for sample purpose only, so that we would not
get an empty list at any point.
In real case, you dont need to create any webhook to make this API call.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> \PayPal\Api\Webhook $webhook */</span>
<span class="hljs-variable">$webhook</span> = <span class="hljs-keyword">require_once</span> <span class="hljs-keyword">__DIR__</span> . <span class="hljs-string">'/../bootstrap.php'</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="get-list-of-all-webhooks">Get List of All Webhooks</h3></div></div></div><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$output</span> = \PayPal\Api\Webhook::getAll(<span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"List all webhooks"</span>, <span class="hljs-string">"WebhookList"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$webhookId</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"List all webhooks"</span>, <span class="hljs-string">"WebhookList"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$output</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$output</span>;</div></div></div></div></body></html>
