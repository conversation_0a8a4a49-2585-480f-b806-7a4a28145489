<!DOCTYPE html><html lang="en"><head><title>notifications/SearchWebhookEvents</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="notifications/SearchWebhookEvents"><meta name="groc-project-path" content="notifications/SearchWebhookEvents.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">notifications/SearchWebhookEvents.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="search-webhook-events-sample">Search Webhook Events Sample</h1>
<p>This sample code demonstrate how to use this call to search for all webhook events., as documented here at:
<a href="https://developer.paypal.com/docs/api/#search-webhook-events">https://developer.paypal.com/docs/api/#search-webhook-events</a>
API used: GET /v1/notifications/webhooks-events</p></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h2 id="get-webhook-instance">Get Webhook Instance</h2>
<h2 id="please-note">PLEASE NOTE:</h2>
<p>Creating webhook is sample purposes only. In real scenario, you dont need to create a new webhook everytime you want to search
for a webhook events. This is made in a sample just to make sure there is minimum of one webhook to listen to.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> \PayPal\Api\Webhook $webhook */</span>
<span class="hljs-variable">$webhook</span> = <span class="hljs-keyword">require</span> <span class="hljs-keyword">__DIR__</span> . <span class="hljs-string">'/../bootstrap.php'</span>;

<span class="hljs-variable">$params</span> = <span class="hljs-keyword">array</span>(</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>&#39;start_time&#39;=&gt;&#39;2014-12-06T11:00:00Z&#39;,
&#39;end_time&#39;=&gt;&#39;2014-12-12T11:00:00Z&#39;</p></div></div><div class="code"><div class="wrapper">);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="search-webhook-events">Search Webhook events</h3></div></div></div><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$output</span> = \PayPal\Api\WebhookEvent::all(<span class="hljs-variable">$params</span>, <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Search Webhook events"</span>, <span class="hljs-string">"WebhookEventList"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Search Webhook events"</span>, <span class="hljs-string">"WebhookEventList"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$params</span>, <span class="hljs-variable">$output</span>);


<span class="hljs-keyword">return</span> <span class="hljs-variable">$output</span>;</div></div></div></div></body></html>
