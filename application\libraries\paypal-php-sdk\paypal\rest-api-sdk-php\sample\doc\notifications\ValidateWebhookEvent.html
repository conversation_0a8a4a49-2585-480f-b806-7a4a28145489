<!DOCTYPE html><html lang="en"><head><title>notifications/ValidateWebhookEvent</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="notifications/ValidateWebhookEvent"><meta name="groc-project-path" content="notifications/ValidateWebhookEvent.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">notifications/ValidateWebhookEvent.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span>

<span class="hljs-keyword">use</span> \<span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">VerifyWebhookSignature</span>;
<span class="hljs-keyword">use</span> \<span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">WebhookEvent</span>;

<span class="hljs-variable">$apiContext</span> = <span class="hljs-keyword">require</span> <span class="hljs-keyword">__DIR__</span> . <span class="hljs-string">'/../bootstrap.php'</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="validate-webhook">Validate Webhook</h1>
<p>PHP Currently does not support certificate chain validation, that is necessary to validate webhook directly, from received data
To resolve that, we need to use alternative, which makes a call to PayPal&#39;s verify-webhook-signature API.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/**
 * This is one way to receive the entire body that you received from PayPal webhook. This is one of the way to retrieve that information.
 * Just uncomment the below line to read the data from actual request.
 */</span>
<span class="hljs-comment">/** <span class="hljs-doctag">@var</span> String $requestBody */</span>
<span class="hljs-variable">$requestBody</span> = <span class="hljs-string">'{"id":"WH-9UG43882HX7271132-6E0871324L7949614","event_version":"1.0","create_time":"2016-09-21T22:00:45Z","resource_type":"sale","event_type":"PAYMENT.SALE.COMPLETED","summary":"Payment completed for $ 21.0 USD","resource":{"id":"80F85758S3080410K","state":"completed","amount":{"total":"21.00","currency":"USD","details":{"subtotal":"17.50","tax":"1.30","shipping":"2.20"}},"payment_mode":"INSTANT_TRANSFER","protection_eligibility":"ELIGIBLE","protection_eligibility_type":"ITEM_NOT_RECEIVED_ELIGIBLE,UNAUTHORIZED_PAYMENT_ELIGIBLE","transaction_fee":{"value":"0.91","currency":"USD"},"invoice_number":"57e3028db8d1b","custom":"","parent_payment":"PAY-7F371669SL612941HK7RQFDQ","create_time":"2016-09-21T21:59:02Z","update_time":"2016-09-21T22:00:06Z","links":[{"href":"https://api.sandbox.paypal.com/v1/payments/sale/80F85758S3080410K","rel":"self","method":"GET"},{"href":"https://api.sandbox.paypal.com/v1/payments/sale/80F85758S3080410K/refund","rel":"refund","method":"POST"},{"href":"https://api.sandbox.paypal.com/v1/payments/payment/PAY-7F371669SL612941HK7RQFDQ","rel":"parent_payment","method":"GET"}]},"links":[{"href":"https://api.sandbox.paypal.com/v1/notifications/webhooks-events/WH-9UG43882HX7271132-6E0871324L7949614","rel":"self","method":"GET"},{"href":"https://api.sandbox.paypal.com/v1/notifications/webhooks-events/WH-9UG43882HX7271132-6E0871324L7949614/resend","rel":"resend","method":"POST"}]}'</span>;
<span class="hljs-comment">/**
* Receive the entire body that you received from PayPal webhook.
* Just uncomment the below line to read the data from actual request.
*/</span>
<span class="hljs-comment">/** <span class="hljs-doctag">@var</span> String $bodyReceived */</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>$bodyReceived = file_get_contents(&#39;php://input&#39;);</p></div></div><div class="code"><div class="wrapper">     
 <span class="hljs-variable">$headers</span> = <span class="hljs-keyword">array</span> (
  <span class="hljs-string">'Client-Pid'</span> =&gt; <span class="hljs-string">'14910'</span>,
  <span class="hljs-string">'Cal-Poolstack'</span> =&gt; <span class="hljs-string">'amqunphttpdeliveryd:UNPHTTPDELIVERY*CalThreadId=0*TopLevelTxnStartTime=1579e71daf8*Host=slcsbamqunphttpdeliveryd3001'</span>,
  <span class="hljs-string">'Correlation-Id'</span> =&gt; <span class="hljs-string">'958be65120106'</span>,
  <span class="hljs-string">'Host'</span> =&gt; <span class="hljs-string">'shiparound-dev.de'</span>,
  <span class="hljs-string">'User-Agent'</span> =&gt; <span class="hljs-string">'PayPal/AUHD-208.0-25552773'</span>,
  <span class="hljs-string">'Paypal-Auth-Algo'</span> =&gt; <span class="hljs-string">'SHA256withRSA'</span>,
  <span class="hljs-string">'Paypal-Cert-Url'</span> =&gt; <span class="hljs-string">'https://api.sandbox.paypal.com/v1/notifications/certs/CERT-360caa42-fca2a594-a5cafa77'</span>,
  <span class="hljs-string">'Paypal-Auth-Version'</span> =&gt; <span class="hljs-string">'v2'</span>,
  <span class="hljs-string">'Paypal-Transmission-Sig'</span> =&gt; <span class="hljs-string">'eDOnWUj9FXOnr2naQnrdL7bhgejVSTwRbwbJ0kuk5wAtm2ZYkr7w5BSUDO7e5ZOsqLwN3sPn3RV85Jd9pjHuTlpuXDLYk+l5qiViPbaaC0tLV+8C/zbDjg2WCfvtf2NmFT8CHgPPQAByUqiiTY+RJZPPQC5np7j7WuxcegsJLeWStRAofsDLiSKrzYV3CKZYtNoNnRvYmSFMkYp/5vk4xGcQLeYNV1CC2PyqraZj8HGG6Y+KV4trhreV9VZDn+rPtLDZTbzUohie1LpEy31k2dg+1szpWaGYOz+MRb40U04oD7fD69vghCrDTYs5AsuFM2+WZtsMDmYGI0pxLjn2yw=='</span>,
  <span class="hljs-string">'Paypal-Transmission-Time'</span> =&gt; <span class="hljs-string">'2016-09-21T22:00:46Z'</span>,
  <span class="hljs-string">'Paypal-Transmission-Id'</span> =&gt; <span class="hljs-string">'d938e770-8046-11e6-8103-6b62a8a99ac4'</span>,
  <span class="hljs-string">'Accept'</span> =&gt; <span class="hljs-string">'*/*'</span>,
);

<span class="hljs-comment">/**
* Receive HTTP headers that you received from PayPal webhook.
* Just uncomment the below line to read the data from actual request.
*/</span>
<span class="hljs-comment">/** <span class="hljs-doctag">@var</span> Array $headers */</span>
<span class="hljs-comment">//$headers = getallheaders();</span>

<span class="hljs-comment">/**
* In Documentions https://developer.paypal.com/docs/api/webhooks/#verify-webhook-signature_post 
* All header keys as UPPERCASE, but I recive the header key as the example array, First letter as UPPERCASE
*/</span>
<span class="hljs-variable">$headers</span> = array_change_key_case(<span class="hljs-variable">$headers</span>, CASE_UPPER);
     
<span class="hljs-variable">$signatureVerification</span> = <span class="hljs-keyword">new</span> VerifyWebhookSignature();
<span class="hljs-variable">$signatureVerification</span>-&gt;setAuthAlgo(<span class="hljs-variable">$headers</span>[<span class="hljs-string">'PAYPAL-AUTH-ALGO'</span>]);
<span class="hljs-variable">$signatureVerification</span>-&gt;setTransmissionId(<span class="hljs-variable">$headers</span>[<span class="hljs-string">'PAYPAL-TRANSMISSION-ID'</span>]);
<span class="hljs-variable">$signatureVerification</span>-&gt;setCertUrl(<span class="hljs-variable">$headers</span>[<span class="hljs-string">'PAYPAL-CERT-URL'</span>]);
<span class="hljs-variable">$signatureVerification</span>-&gt;setWebhookId(<span class="hljs-string">"9XL90610J3647323C"</span>); <span class="hljs-comment">// Note that the Webhook ID must be a currently valid Webhook that you created with your client ID/secret.</span>
<span class="hljs-variable">$signatureVerification</span>-&gt;setTransmissionSig(<span class="hljs-variable">$headers</span>[<span class="hljs-string">'PAYPAL-TRANSMISSION-SIG'</span>]);
<span class="hljs-variable">$signatureVerification</span>-&gt;setTransmissionTime(<span class="hljs-variable">$headers</span>[<span class="hljs-string">'PAYPAL-TRANSMISSION-TIME'</span>]);

<span class="hljs-variable">$webhookEvent</span> = <span class="hljs-keyword">new</span> WebhookEvent();
<span class="hljs-variable">$webhookEvent</span>-&gt;fromJson(<span class="hljs-variable">$requestBody</span>);
<span class="hljs-variable">$signatureVerification</span>-&gt;setWebhookEvent(<span class="hljs-variable">$webhookEvent</span>);
<span class="hljs-variable">$request</span> = <span class="hljs-keyword">clone</span> <span class="hljs-variable">$signatureVerification</span>;

<span class="hljs-keyword">try</span> {
    <span class="hljs-comment">/** <span class="hljs-doctag">@var</span> \PayPal\Api\VerifyWebhookSignatureResponse $output */</span>
    <span class="hljs-variable">$output</span> = <span class="hljs-variable">$signatureVerification</span>-&gt;post(<span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Validate Received Webhook Event"</span>, <span class="hljs-string">"WebhookEvent"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$request</span>-&gt;toJSON(), <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">ResultPrinter::printResult(<span class="hljs-string">"Validate Received Webhook Event"</span>, <span class="hljs-string">"WebhookEvent"</span>, <span class="hljs-variable">$output</span>-&gt;getVerificationStatus(), <span class="hljs-variable">$request</span>-&gt;toJSON(), <span class="hljs-variable">$output</span>);</div></div></div></div></body></html>
