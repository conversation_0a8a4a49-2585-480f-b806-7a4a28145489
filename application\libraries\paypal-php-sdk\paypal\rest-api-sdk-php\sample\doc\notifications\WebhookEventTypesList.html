<!DOCTYPE html><html lang="en"><head><title>notifications/WebhookEventTypesList</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="notifications/WebhookEventTypesList"><meta name="groc-project-path" content="notifications/WebhookEventTypesList.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">notifications/WebhookEventTypesList.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="get-reference-list-of-all-webhook-event-types">Get Reference List of all Webhook Event Types</h1>
<p>This sample code demonstrate how you can get reference list of all webhook event types, as documented here at:
<a href="https://developer.paypal.com/webapps/developer/docs/api/#get-a-reference-list-of-webhook-event-types">https://developer.paypal.com/webapps/developer/docs/api/#get-a-reference-list-of-webhook-event-types</a>
API used: GET /v1/notifications/webhooks-event-types</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$apiContext</span> = <span class="hljs-keyword">require</span> <span class="hljs-keyword">__DIR__</span> . <span class="hljs-string">'/../bootstrap.php'</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="get-list-of-all-webhook-event-types">Get List of all Webhook event types</h3></div></div></div><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$output</span> = \PayPal\Api\WebhookEventType::availableEventTypes(<span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Get List of All Webhook Event Types"</span>, <span class="hljs-string">"WebhookEventTypeList"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Get List of All Webhook Event Types"</span>, <span class="hljs-string">"WebhookEventTypeList"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$output</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$output</span>;</div></div></div></div></body></html>
