<!DOCTYPE html><html lang="en"><head><title>payment-experience/CreateWebProfile</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="payment-experience/CreateWebProfile"><meta name="groc-project-path" content="payment-experience/CreateWebProfile.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">payment-experience/CreateWebProfile.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span>

<span class="hljs-keyword">require</span> <span class="hljs-keyword">__DIR__</span> . <span class="hljs-string">'/../bootstrap.php'</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="create-web-profile">Create Web Profile</h3>
<p>Use the /web-profiles resource to create seamless payment experience profiles. See the payment experience overview for further information about using the /payment resource to create the PayPal payment and pass the experience_profile_id.
Documentation available at <a href="https://developer.paypal.com/webapps/developer/docs/api/#create-a-web-experience-profile">https://developer.paypal.com/webapps/developer/docs/api/#create-a-web-experience-profile</a></p></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Lets create an instance of FlowConfig and add
landing page type information</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$flowConfig</span> = <span class="hljs-keyword">new</span> \PayPal\Api\FlowConfig();</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Type of PayPal page to be displayed when a user lands on the PayPal site for checkout. Allowed values: Billing or Login. When set to Billing, the Non-PayPal account landing page is used. When set to Login, the PayPal account login landing page is used.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$flowConfig</span>-&gt;setLandingPageType(<span class="hljs-string">"Billing"</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>The URL on the merchant site for transferring to after a bank transfer payment.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$flowConfig</span>-&gt;setBankTxnPendingUrl(<span class="hljs-string">"http://www.yeowza.com/"</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>When set to &quot;commit&quot;, the buyer is shown an amount, and the button text will read &quot;Pay Now&quot; on the checkout page.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$flowConfig</span>-&gt;setUserAction(<span class="hljs-string">"commit"</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Defines the HTTP method to use to redirect the user to a return URL. A valid value is <code>GET</code> or <code>POST</code>.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$flowConfig</span>-&gt;setReturnUriHttpMethod(<span class="hljs-string">"GET"</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Parameters for style and presentation.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$presentation</span> = <span class="hljs-keyword">new</span> \PayPal\Api\Presentation();</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>A URL to logo image. Allowed vaues: .gif, .jpg, or .png.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$presentation</span>-&gt;setLogoImage(<span class="hljs-string">"http://www.yeowza.com/favico.ico"</span>)</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>A label that overrides the business name in the PayPal account on the PayPal pages.</p></div></div><div class="code"><div class="wrapper">    -&gt;setBrandName(<span class="hljs-string">"YeowZa! Paypal"</span>)</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p> Locale of pages displayed by PayPal payment experience.</p></div></div><div class="code"><div class="wrapper">    -&gt;setLocaleCode(<span class="hljs-string">"US"</span>)</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>A label to use as hypertext for the return to merchant link.</p></div></div><div class="code"><div class="wrapper">    -&gt;setReturnUrlLabel(<span class="hljs-string">"Return"</span>)</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>A label to use as the title for the note to seller field. Used only when <code>allow_note</code> is <code>1</code>.</p></div></div><div class="code"><div class="wrapper">    -&gt;setNoteToSellerLabel(<span class="hljs-string">"Thanks!"</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Parameters for input fields customization.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$inputFields</span> = <span class="hljs-keyword">new</span> \PayPal\Api\InputFields();</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Enables the buyer to enter a note to the merchant on the PayPal page during checkout.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$inputFields</span>-&gt;setAllowNote(<span class="hljs-keyword">true</span>)</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Determines whether or not PayPal displays shipping address fields on the experience pages. Allowed values: 0, 1, or 2. When set to 0, PayPal displays the shipping address on the PayPal pages. When set to 1, PayPal does not display shipping address fields whatsoever. When set to 2, if you do not pass the shipping address, PayPal obtains it from the buyer’s account profile. For digital goods, this field is required, and you must set it to 1.</p></div></div><div class="code"><div class="wrapper">    -&gt;setNoShipping(<span class="hljs-number">1</span>)</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Determines whether or not the PayPal pages should display the shipping address and not the shipping address on file with PayPal for this buyer. Displaying the PayPal street address on file does not allow the buyer to edit that address. Allowed values: 0 or 1. When set to 0, the PayPal pages should not display the shipping address. When set to 1, the PayPal pages should display the shipping address.</p></div></div><div class="code"><div class="wrapper">    -&gt;setAddressOverride(<span class="hljs-number">0</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h4 id="payment-web-experience-profile-resource">Payment Web experience profile resource</h4></div></div></div><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-variable">$webProfile</span> = <span class="hljs-keyword">new</span> \PayPal\Api\WebProfile();</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Name of the web experience profile. Required. Must be unique</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$webProfile</span>-&gt;setName(<span class="hljs-string">"YeowZa! T-Shirt Shop"</span> . uniqid())</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Parameters for flow configuration.</p></div></div><div class="code"><div class="wrapper">    -&gt;setFlowConfig(<span class="hljs-variable">$flowConfig</span>)</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Parameters for style and presentation.</p></div></div><div class="code"><div class="wrapper">    -&gt;setPresentation(<span class="hljs-variable">$presentation</span>)</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Parameters for input field customization.</p></div></div><div class="code"><div class="wrapper">    -&gt;setInputFields(<span class="hljs-variable">$inputFields</span>)</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Indicates whether the profile persists for three hours or permanently. Set to <code>false</code> to persist the profile permanently. Set to <code>true</code> to persist the profile for three hours.</p></div></div><div class="code"><div class="wrapper">    -&gt;setTemporary(<span class="hljs-keyword">true</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>For Sample Purposes Only.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$request</span> = <span class="hljs-keyword">clone</span> <span class="hljs-variable">$webProfile</span>;

<span class="hljs-keyword">try</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Use this call to create a profile.</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$createProfileResponse</span> = <span class="hljs-variable">$webProfile</span>-&gt;create(<span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (\PayPal\<span class="hljs-keyword">Exception</span>\PayPalConnectionException <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Created Web Profile"</span>, <span class="hljs-string">"Web Profile"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$request</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Created Web Profile"</span>, <span class="hljs-string">"Web Profile"</span>, <span class="hljs-variable">$createProfileResponse</span>-&gt;getId(), <span class="hljs-variable">$request</span>, <span class="hljs-variable">$createProfileResponse</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$createProfileResponse</span>;</div></div></div></div></body></html>
