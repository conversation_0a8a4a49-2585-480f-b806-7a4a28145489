<!DOCTYPE html><html lang="en"><head><title>payment-experience/GetWebProfile</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="payment-experience/GetWebProfile"><meta name="groc-project-path" content="payment-experience/GetWebProfile.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">payment-experience/GetWebProfile.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="get-web-profile">Get Web Profile</h3>
<p>If your request is successful, the API returns a web_profile object response that contains the profile details.
Documentation available at <a href="https://developer.paypal.com/webapps/developer/docs/api/#retrieve-a-web-experience-profile">https://developer.paypal.com/webapps/developer/docs/api/#retrieve-a-web-experience-profile</a></p></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>We are going to re-use the sample code from CreateWebProfile.php.
If you have not visited the sample yet, please visit it before trying GetWebProfile.php
The CreateWebProfile.php will create a web profile for us, and return a CreateProfileResponse,
that contains the web profile ID.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> \PayPal\Api\CreateProfileResponse $result */</span>
<span class="hljs-variable">$createProfileResponse</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'CreateWebProfile.php'</span>;

<span class="hljs-keyword">try</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>If your request is successful, the API returns a web_profile object response that contains the profile details.</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$webProfile</span> = \PayPal\Api\WebProfile::get(<span class="hljs-variable">$createProfileResponse</span>-&gt;getId(), <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (\PayPal\<span class="hljs-keyword">Exception</span>\PayPalConnectionException <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Get Web Profile"</span>, <span class="hljs-string">"Web Profile"</span>, <span class="hljs-variable">$webProfile</span>-&gt;getId(), <span class="hljs-keyword">null</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Get Web Profile"</span>, <span class="hljs-string">"Web Profile"</span>, <span class="hljs-variable">$webProfile</span>-&gt;getId(), <span class="hljs-keyword">null</span>, <span class="hljs-variable">$webProfile</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$webProfile</span>;</div></div></div></div></body></html>
