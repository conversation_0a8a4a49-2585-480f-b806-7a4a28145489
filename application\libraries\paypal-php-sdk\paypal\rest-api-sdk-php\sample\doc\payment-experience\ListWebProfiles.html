<!DOCTYPE html><html lang="en"><head><title>payment-experience/ListWebProfiles</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="payment-experience/ListWebProfiles"><meta name="groc-project-path" content="payment-experience/ListWebProfiles.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">payment-experience/ListWebProfiles.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span>

<span class="hljs-variable">$apiContext</span> = <span class="hljs-keyword">require</span> <span class="hljs-keyword">__DIR__</span> . <span class="hljs-string">'/../bootstrap.php'</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="retrieve-list-of-all-web-profiles">Retrieve List of All Web Profiles</h3>
<p>Documentation available at <a href="https://developer.paypal.com/webapps/developer/docs/api/#list-web-experience-profiles">https://developer.paypal.com/webapps/developer/docs/api/#list-web-experience-profiles</a></p></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Retrieve the list of all web profiles by calling the
static <code>get_list</code> method on the WebProfile class.
(See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$list</span> = \PayPal\Api\WebProfile::get_list(<span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (\PayPal\<span class="hljs-keyword">Exception</span>\PayPalConnectionException <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Get List of All Web Profiles"</span>, <span class="hljs-string">"Web Profiles"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}
<span class="hljs-variable">$result</span> = <span class="hljs-string">''</span>;
<span class="hljs-keyword">foreach</span> (<span class="hljs-variable">$list</span> <span class="hljs-keyword">as</span> <span class="hljs-variable">$object</span>) {
    <span class="hljs-variable">$result</span> .= <span class="hljs-variable">$object</span>-&gt;toJSON(<span class="hljs-number">128</span>) . PHP_EOL;
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Get List of All Web Profiles"</span>, <span class="hljs-string">"Web Profiles"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$result</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$list</span>;</div></div></div></div></body></html>
