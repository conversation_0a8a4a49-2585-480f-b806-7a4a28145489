<!DOCTYPE html><html lang="en"><head><title>payment-experience/PartiallyUpdateWebProfile</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="payment-experience/PartiallyUpdateWebProfile"><meta name="groc-project-path" content="payment-experience/PartiallyUpdateWebProfile.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">payment-experience/PartiallyUpdateWebProfile.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h4 id="partially-update-web-profile">Partially Update Web Profile</h4>
<p>Use this call to partially update a web experience profile.
Documentation available at <a href="https://developer.paypal.com/webapps/developer/docs/api/#partially-update-a-web-experience-profile">https://developer.paypal.com/webapps/developer/docs/api/#partially-update-a-web-experience-profile</a></p></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>We will be re-using the sample code to get a web profile. GetWebProfile.php will
create a new web profileId for sample, and return the web profile object.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> \PayPal\Api\WebProfile $webProfile */</span>
<span class="hljs-variable">$webProfile</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'GetWebProfile.php'</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="create-patch-operation">Create Patch Operation</h3>
<p>APIs allows us to pass an array of patches
to make patch operations.
Each Patch operation can be created by using Patch Class
as shown below</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$patchOperation1</span> = <span class="hljs-keyword">new</span> \PayPal\Api\Patch();</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>The operation to perform. Required. Allowed values: add, remove, replace, move, copy, test</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$patchOperation1</span>-&gt;setOp(<span class="hljs-string">"add"</span>)</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>string containing a JSON-Pointer value that references a location within the target document (the target location) where the operation is performed. Required.</p></div></div><div class="code"><div class="wrapper">    -&gt;setPath(<span class="hljs-string">"/presentation/brand_name"</span>)</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>New value to apply based on the operation.</p></div></div><div class="code"><div class="wrapper">    -&gt;setValue(<span class="hljs-string">"New Brand Name"</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Similar patch operation to remove the landing page type</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$patchOperation2</span> = <span class="hljs-keyword">new</span> \PayPal\Api\Patch();
<span class="hljs-variable">$patchOperation2</span>-&gt;setOp(<span class="hljs-string">"remove"</span>)
    -&gt;setPath(<span class="hljs-string">"/flow_config/landing_page_type"</span>);


<span class="hljs-comment">//Generate an array of patch operations</span>
<span class="hljs-variable">$patches</span> = <span class="hljs-keyword">array</span>(<span class="hljs-variable">$patchOperation1</span>, <span class="hljs-variable">$patchOperation2</span>);

<span class="hljs-keyword">try</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Execute the partial update, to carry out these two operations on a given web profile object</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-keyword">if</span> (<span class="hljs-variable">$webProfile</span>-&gt;partial_update(<span class="hljs-variable">$patches</span>, <span class="hljs-variable">$apiContext</span>)) {
        <span class="hljs-variable">$webProfile</span> = \PayPal\Api\WebProfile::get(<span class="hljs-variable">$webProfile</span>-&gt;getId(), <span class="hljs-variable">$apiContext</span>);
    }
} <span class="hljs-keyword">catch</span> (\<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Partially Updated Web Profile"</span>, <span class="hljs-string">"Web Profile"</span>, <span class="hljs-variable">$webProfile</span>-&gt;getId(), <span class="hljs-variable">$patches</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Partially Updated Web Profile"</span>, <span class="hljs-string">"Web Profile"</span>, <span class="hljs-variable">$webProfile</span>-&gt;getId(), <span class="hljs-variable">$patches</span>, <span class="hljs-variable">$webProfile</span>);</div></div></div></div></body></html>
