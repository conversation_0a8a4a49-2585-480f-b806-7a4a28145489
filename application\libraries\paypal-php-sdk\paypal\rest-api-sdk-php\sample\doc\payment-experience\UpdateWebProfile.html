<!DOCTYPE html><html lang="en"><head><title>payment-experience/UpdateWebProfile</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="payment-experience/UpdateWebProfile"><meta name="groc-project-path" content="payment-experience/UpdateWebProfile.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">payment-experience/UpdateWebProfile.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h4 id="update-web-profile">Update Web Profile</h4>
<p>Use this call to update an experience profile.
Documentation available at <a href="https://developer.paypal.com/webapps/developer/docs/api/#update-a-web-experience-profile">https://developer.paypal.com/webapps/developer/docs/api/#update-a-web-experience-profile</a></p></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>We will be re-using the sample code to get a web profile. GetWebProfile.php will
create a new web profileId for sample, and return the web profile object.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> \PayPal\Api\WebProfile $webProfile */</span>
<span class="hljs-variable">$webProfile</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'GetWebProfile.php'</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Updated the logo image of presentation object in a given web profile.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$webProfile</span>-&gt;getPresentation()-&gt;setLogoImage(<span class="hljs-string">"http://www.google.com/favico.ico"</span>);

<span class="hljs-keyword">try</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Update the web profile to change the logo image.</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-keyword">if</span> (<span class="hljs-variable">$webProfile</span>-&gt;update(<span class="hljs-variable">$apiContext</span>)) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>If the update is successfull, we can now get the object, and verify the web profile
object</p></div></div><div class="code"><div class="wrapper">        <span class="hljs-variable">$updatedWebProfile</span> = \PayPal\Api\WebProfile::get(<span class="hljs-variable">$webProfile</span>-&gt;getId(), <span class="hljs-variable">$apiContext</span>);
    }
} <span class="hljs-keyword">catch</span> (\<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Updated Web Profile"</span>, <span class="hljs-string">"Web Profile"</span>, <span class="hljs-variable">$webProfile</span>-&gt;getId(), <span class="hljs-variable">$webProfile</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Updated Web Profile"</span>, <span class="hljs-string">"Web Profile"</span>, <span class="hljs-variable">$updatedWebProfile</span>-&gt;getId(), <span class="hljs-variable">$webProfile</span>, <span class="hljs-variable">$updatedWebProfile</span>);</div></div></div></div></body></html>
