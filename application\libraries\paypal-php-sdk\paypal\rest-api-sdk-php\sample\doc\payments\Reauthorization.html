<!DOCTYPE html><html lang="en"><head><title>payments/Reauthorization</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="payments/Reauthorization"><meta name="groc-project-path" content="payments/Reauthorization.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">payments/Reauthorization.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h2 id="reauthorization-sample">Reauthorization Sample</h2>
<p>This sample code demonstrates how you can reauthorize a PayPal
account payment.
API used: v1/payments/authorization/{authorization_id}/reauthorize</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> Authorization $authorization */</span>
<span class="hljs-variable">$authorization</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'AuthorizePayment.php'</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Amount</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Authorization</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="reauthorization">Reauthorization</h3>
<p>Reauthorization is available only for PayPal account payments
and not for credit card payments.</p></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>You can reauthorize a payment only once 4 to 29
days after the 3-day honor period for the original authorization
has expired.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$amount</span> = <span class="hljs-keyword">new</span> Amount();
    <span class="hljs-variable">$amount</span>-&gt;setCurrency(<span class="hljs-string">"USD"</span>);
    <span class="hljs-variable">$amount</span>-&gt;setTotal(<span class="hljs-number">1</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="reauthorize-with-amount-being-reauthorized">Reauthorize with amount being reauthorized</h3></div></div></div><div class="segment"><div class="code"><div class="wrapper">    <span class="hljs-variable">$authorization</span>-&gt;setAmount(<span class="hljs-variable">$amount</span>);

    <span class="hljs-variable">$reAuthorization</span> = <span class="hljs-variable">$authorization</span>-&gt;reauthorize(<span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Reauthorize Payment"</span>, <span class="hljs-string">"Payment"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">ResultPrinter::printResult(<span class="hljs-string">"Reauthorize Payment"</span>, <span class="hljs-string">"Payment"</span>, <span class="hljs-variable">$authorization</span>-&gt;getId(), <span class="hljs-keyword">null</span>, <span class="hljs-variable">$reAuthorization</span>);</div></div></div></div></body></html>
