<!DOCTYPE html><html lang="en"><head><title>payments/RefundCapture</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="payments/RefundCapture"><meta name="groc-project-path" content="payments/RefundCapture.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">payments/RefundCapture.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="refund-capture-sample">Refund Capture Sample</h1>
<p>This sample code demonstrates how you can
process a refund on a Captured transaction.
API used: /v1/payments/capture/{<captureID>}/refund</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> Capture $capture */</span>
<span class="hljs-variable">$capture</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'AuthorizationCapture.php'</span>;

<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Capture</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Refund</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">RefundRequest</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="refund">Refund</h3>
<p>Create a refund object indicating
refund amount and call the refund method</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$refundRequest</span> = <span class="hljs-keyword">new</span> RefundRequest();
<span class="hljs-variable">$refundRequest</span>-&gt;setAmount(<span class="hljs-variable">$amt</span>);

<span class="hljs-keyword">try</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Create a new apiContext object so we send a new
PayPal-Request-Id (idempotency) header for this resource</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$apiContext</span> = getApiContext(<span class="hljs-variable">$clientId</span>, <span class="hljs-variable">$clientSecret</span>);

    <span class="hljs-variable">$captureRefund</span> = <span class="hljs-variable">$capture</span>-&gt;refundCapturedPayment(<span class="hljs-variable">$refundRequest</span>, <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Refund Capture"</span>, <span class="hljs-string">"Capture"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$refundRequest</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">ResultPrinter::printResult(<span class="hljs-string">"Refund Capture"</span>, <span class="hljs-string">"Capture"</span>, <span class="hljs-variable">$captureRefund</span>-&gt;getId(), <span class="hljs-variable">$refundRequest</span>, <span class="hljs-variable">$captureRefund</span>);</div></div></div></div></body></html>
