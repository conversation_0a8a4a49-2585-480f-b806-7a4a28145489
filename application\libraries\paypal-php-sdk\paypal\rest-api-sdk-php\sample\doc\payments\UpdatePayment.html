<!DOCTYPE html><html lang="en"><head><title>payments/UpdatePayment</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="payments/UpdatePayment"><meta name="groc-project-path" content="payments/UpdatePayment.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">payments/UpdatePayment.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="update-payment-sample">Update Payment Sample</h1>
<p>This sample code demonstrate how you can
update a Payment resources
you&#39;ve created using the Payments API.</p>
<h2 id="note">NOTE</h2>
<p>Note that it can only be updated before the execute is done. Once, the payment is executed it is not
possible to udpate that.
Docs: <a href="https://developer.paypal.com/webapps/developer/docs/api/#update-a-payment-resource">https://developer.paypal.com/webapps/developer/docs/api/#update-a-payment-resource</a>
API used: PATCH /v1/payments/payment/<Payment-Id></p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> Payment $createdPayment */</span>
<span class="hljs-variable">$createdPayment</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'CreatePaymentUsingPayPal.php'</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Payment</span>;

<span class="hljs-variable">$paymentId</span> = <span class="hljs-variable">$createdPayment</span>-&gt;getId();</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h4 id="create-a-patch-request">Create a Patch Request</h4>
<p>This is how the data would look like:
   [
           {
               &quot;op&quot;: &quot;replace&quot;,
               &quot;path&quot;: &quot;/transactions/0/amount&quot;,
               &quot;value&quot;: {
               &quot;total&quot;: &quot;25.00&quot;,
                   &quot;currency&quot;: &quot;USD&quot;,
                   &quot;details&quot;: {
                   &quot;subtotal&quot;: &quot;17.50&quot;,
                       &quot;shipping&quot;: &quot;6.20&quot;,
                       &quot;tax&quot;: &quot;1.30&quot;
                   }
               }
           },
           {
               &quot;op&quot;: &quot;add&quot;,
               &quot;path&quot;: &quot;/transactions/0/item_list/shipping_address&quot;,
               &quot;value&quot;: {
               &quot;recipient_name&quot;: &quot;Gruneberg, Anna&quot;,
                   &quot;line1&quot;: &quot;52 N Main St&quot;,
                   &quot;city&quot;: &quot;San Jose&quot;,
                   &quot;postal_code&quot;: &quot;95112&quot;,
                   &quot;country_code&quot;: &quot;US&quot;,
                   &quot;state&quot;: &quot;CA&quot;
               }
           }
       ]</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$patchReplace</span> = <span class="hljs-keyword">new</span> \PayPal\Api\Patch();
<span class="hljs-variable">$patchReplace</span>-&gt;setOp(<span class="hljs-string">'replace'</span>)
    -&gt;setPath(<span class="hljs-string">'/transactions/0/amount'</span>)
    -&gt;setValue(json_decode(<span class="hljs-string">'{
                    "total": "25.00",
                    "currency": "USD",
                    "details": {
                        "subtotal": "17.50",
                        "shipping": "6.20",
                        "tax":"1.30"
                    }
                }'</span>));

<span class="hljs-variable">$patchAdd</span> = <span class="hljs-keyword">new</span> \PayPal\Api\Patch();
<span class="hljs-variable">$patchAdd</span>-&gt;setOp(<span class="hljs-string">'add'</span>)
    -&gt;setPath(<span class="hljs-string">'/transactions/0/item_list/shipping_address'</span>)
    -&gt;setValue(json_decode(<span class="hljs-string">'{
                    "recipient_name": "Gruneberg, Anna",
                    "line1": "52 N Main St",
                    "city": "San Jose",
                    "state": "CA",
                    "postal_code": "95112",
                    "country_code": "US"
                }'</span>));

<span class="hljs-variable">$patchRequest</span> = <span class="hljs-keyword">new</span> \PayPal\Api\PatchRequest();
<span class="hljs-variable">$patchRequest</span>-&gt;setPatches(<span class="hljs-keyword">array</span>(<span class="hljs-variable">$patchReplace</span>, <span class="hljs-variable">$patchAdd</span>));</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="update-payment">Update payment</h3>
<p>Update payment object by calling the
static <code>update</code> method
on the Payment class by passing a valid
Payment ID
(See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$result</span> = <span class="hljs-variable">$createdPayment</span>-&gt;update(<span class="hljs-variable">$patchRequest</span>, <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Update Payment"</span>, <span class="hljs-string">"PatchRequest"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$patchRequest</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Update Payment"</span>, <span class="hljs-string">"PatchRequest"</span>, <span class="hljs-variable">$payment</span>-&gt;getId(), <span class="hljs-variable">$patchRequest</span>, <span class="hljs-keyword">null</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="getting-updated-payment-object">Getting Updated Payment Object</h3></div></div></div><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-keyword">if</span> (<span class="hljs-variable">$result</span> == <span class="hljs-keyword">true</span>) {
    <span class="hljs-variable">$result</span> = Payment::get(<span class="hljs-variable">$createdPayment</span>-&gt;getId(), <span class="hljs-variable">$apiContext</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Get Payment"</span>, <span class="hljs-string">"Payment"</span>, <span class="hljs-variable">$result</span>-&gt;getId(), <span class="hljs-keyword">null</span>, <span class="hljs-variable">$result</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="get-redirect-url">Get redirect url</h3>
<p>The API response provides the url that you must redirect
the buyer to. Retrieve the url from the $payment-&gt;getLinks()
method</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">foreach</span> (<span class="hljs-variable">$result</span>-&gt;getLinks() <span class="hljs-keyword">as</span> <span class="hljs-variable">$link</span>) {
    <span class="hljs-keyword">if</span> (<span class="hljs-variable">$link</span>-&gt;getRel() == <span class="hljs-string">'approval_url'</span>) {
        <span class="hljs-variable">$approvalUrl</span> = <span class="hljs-variable">$link</span>-&gt;getHref();
        <span class="hljs-keyword">break</span>;
    }
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Created Payment Using PayPal. Please visit the URL to Approve."</span>, <span class="hljs-string">"Payment"</span>, <span class="hljs-string">"&lt;a href='$approvalUrl' &gt;$approvalUrl&lt;/a&gt;"</span>, <span class="hljs-variable">$request</span>, <span class="hljs-variable">$result</span>);
}

<span class="hljs-keyword">return</span> <span class="hljs-variable">$result</span>;</div></div></div></div></body></html>
