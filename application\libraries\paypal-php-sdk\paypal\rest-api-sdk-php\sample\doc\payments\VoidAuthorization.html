<!DOCTYPE html><html lang="en"><head><title>payments/VoidAuthorization</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="payments/VoidAuthorization"><meta name="groc-project-path" content="payments/VoidAuthorization.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">payments/VoidAuthorization.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="voidauthorization">VoidAuthorization</h1>
<p>This sample code demonstrates how you can 
void an authorized payment.
API used: /v1/payments/authorization/&lt;{authorizationid}&gt;/void&quot;</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> Authorization $authorization */</span>
<span class="hljs-variable">$authorization</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'AuthorizePayment.php'</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Replace $authorizationid with any static Id you might already have. It will do a void on it</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$authorizationId</span> = <span class="hljs-string">'1BF65516U6866543H'</span>; <span class="hljs-comment">// $authorization-&gt;getId();</span>
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Authorization</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="voidauthorization">VoidAuthorization</h3>
<p>You can void a previously authorized payment
by invoking the $authorization-&gt;void method
with a valid ApiContext (See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Lookup the authorization</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$authorization</span> = Authorization::get(<span class="hljs-variable">$authorizationId</span>, <span class="hljs-variable">$apiContext</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Void the authorization</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$voidedAuth</span> = <span class="hljs-variable">$authorization</span>-&gt;void(<span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Void Authorization"</span>, <span class="hljs-string">"Authorization"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Void Authorization"</span>, <span class="hljs-string">"Authorization"</span>, <span class="hljs-variable">$voidedAuth</span>-&gt;getId(), <span class="hljs-keyword">null</span>, <span class="hljs-variable">$voidedAuth</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$voidedAuth</span>;</div></div></div></div></body></html>
