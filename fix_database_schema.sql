-- Script untuk memperbaiki schema database tbl_sales
-- Menghilangkan CURRENT_TIMESTAMP otomatis pada kolom date_time
-- Author: AI Assistant
-- Date: 2025-08-11

-- Backup struktur tabel sebelum perubahan
-- SHOW CREATE TABLE tbl_sales;

-- Ubah kolom date_time untuk menghilangkan DEFAULT CURRENT_TIMESTAMP
-- dan ON UPDATE CURRENT_TIMESTAMP
ALTER TABLE tbl_sales 
MODIFY COLUMN date_time timestamp NOT NULL;

-- Verifikasi perubahan struktur
DESCRIBE tbl_sales;

-- <PERSON><PERSON> apakah masih ada DEFAULT CURRENT_TIMESTAMP
SHOW CREATE TABLE tbl_sales;

-- Jika ingin menambahkan index untuk performa query berdasarkan tanggal
-- CREATE INDEX idx_sales_date_time ON tbl_sales(date_time);
-- CREATE INDEX idx_sales_sale_date ON tbl_sales(sale_date);

-- Query untuk memastikan konsistensi data setelah perubahan
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN sale_date IS NOT NULL AND date_time IS NOT NULL THEN 1 END) as complete_records,
    COUNT(CASE WHEN sale_date IS NULL OR date_time IS NULL THEN 1 END) as incomplete_records
FROM tbl_sales 
WHERE del_status = 'Live';
