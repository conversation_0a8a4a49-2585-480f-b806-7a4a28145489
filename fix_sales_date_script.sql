-- <PERSON><PERSON><PERSON> untuk memperbaiki kolom sale_date di tbl_sales
-- Patokan: date_time (timezone Asia/Singapore UTC+8)
-- Author: AI Assistant
-- Date: 2025-08-11

-- Backup data sebelum update (opsional)
-- CREATE TABLE tbl_sales_backup AS SELECT * FROM tbl_sales;

-- Update sale_date berdasarkan date_time dengan timezone Asia/Singapore
-- Menggunakan CONVERT_TZ untuk konversi dari UTC ke Asia/Singapore
UPDATE tbl_sales 
SET sale_date = DATE(CONVERT_TZ(date_time, '+00:00', '+08:00'))
WHERE del_status = 'Live';

-- Jika server MySQL tidak support CONVERT_TZ, gunakan alternatif ini:
-- UPDATE tbl_sales 
-- SET sale_date = DATE(DATE_ADD(date_time, INTERVAL 8 HOUR))
-- WHERE del_status = 'Live';

-- <PERSON><PERSON><PERSON><PERSON><PERSON> hasil update
SELECT 
    id,
    sale_no,
    sale_date,
    date_time,
    DATE(CONVERT_TZ(date_time, '+00:00', '+08:00')) as calculated_date,
    CASE 
        WHEN sale_date = DATE(CONVERT_TZ(date_time, '+00:00', '+08:00')) THEN 'MATCH' 
        ELSE 'MISMATCH' 
    END as status
FROM tbl_sales 
WHERE del_status = 'Live'
ORDER BY id DESC
LIMIT 20;

-- Query untuk cek data yang masih bermasalah
SELECT 
    COUNT(*) as total_records,
    SUM(CASE WHEN sale_date = DATE(CONVERT_TZ(date_time, '+00:00', '+08:00')) THEN 1 ELSE 0 END) as matching_records,
    SUM(CASE WHEN sale_date != DATE(CONVERT_TZ(date_time, '+00:00', '+08:00')) THEN 1 ELSE 0 END) as mismatched_records
FROM tbl_sales 
WHERE del_status = 'Live';
