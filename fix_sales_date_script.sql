-- <PERSON><PERSON>t untuk memperbaiki kolom sale_date di tbl_sales
-- Patokan: date_time (timezone Asia/Singapore UTC+8)
-- Author: AI Assistant
-- Date: 2025-08-11

-- Backup data sebelum update (opsional)
-- CREATE TABLE tbl_sales_backup AS SELECT * FROM tbl_sales;

-- Update sale_date berdasarkan date_time (langsung dari date_time tanpa konversi timezone)
-- <PERSON>a date_time sudah dalam timezone yang benar (Asia/Singapore)
UPDATE tbl_sales
SET sale_date = DATE(date_time)
WHERE del_status = 'Live';

-- Alternatif jika date_time masih dalam UTC dan perlu konversi ke Asia/Singapore:
-- UPDATE tbl_sales
-- SET sale_date = DATE(CONVERT_TZ(date_time, '+00:00', '+08:00'))
-- WHERE del_status = 'Live';

-- Alternatif jika server tidak support CONVERT_TZ:
-- UPDATE tbl_sales
-- SET sale_date = DATE(DATE_ADD(date_time, INTERVAL 8 HOUR))
-- WHERE del_status = 'Live';

-- <PERSON><PERSON>fi<PERSON><PERSON> hasil update
SELECT
    id,
    sale_no,
    sale_date,
    date_time,
    DATE(date_time) as calculated_date,
    CASE
        WHEN sale_date = DATE(date_time) THEN 'MATCH'
        ELSE 'MISMATCH'
    END as status
FROM tbl_sales
WHERE del_status = 'Live'
ORDER BY id DESC
LIMIT 20;

-- Query untuk cek data yang masih bermasalah
SELECT
    COUNT(*) as total_records,
    SUM(CASE WHEN sale_date = DATE(date_time) THEN 1 ELSE 0 END) as matching_records,
    SUM(CASE WHEN sale_date != DATE(date_time) THEN 1 ELSE 0 END) as mismatched_records
FROM tbl_sales
WHERE del_status = 'Live';
