-- <PERSON><PERSON><PERSON> se<PERSON>hana untuk memperbaiki sale_date berdasarkan date_time
-- Author: AI Assistant
-- Date: 2025-08-11

-- BACKUP DATA TERLEBIH DAHULU (WAJIB!)
-- CREATE TABLE tbl_sales_backup AS SELECT * FROM tbl_sales WHERE del_status = 'Live';

-- Cek data sebelum update
SELECT 'BEFORE UPDATE' as status, COUNT(*) as total_records FROM tbl_sales WHERE del_status = 'Live';

-- <PERSON>pi<PERSON><PERSON> contoh data yang akan diupdate
SELECT 
    id, sale_no, sale_date, date_time, DATE(date_time) as should_be_sale_date
FROM tbl_sales 
WHERE del_status = 'Live' 
AND sale_date != DATE(date_time)
ORDER BY id DESC 
LIMIT 10;

-- UPDATE UTAMA: Set sale_date = tanggal dari date_time
UPDATE tbl_sales 
SET sale_date = DATE(date_time)
WHERE del_status = 'Live';

-- <PERSON><PERSON><PERSON><PERSON><PERSON> hasil update
SELECT 
    'AFTER UPDATE' as status,
    COUNT(*) as total_records,
    SUM(CASE WHEN sale_date = DATE(date_time) THEN 1 ELSE 0 END) as matching_records,
    SUM(CASE WHEN sale_date != DATE(date_time) THEN 1 ELSE 0 END) as mismatched_records
FROM tbl_sales 
WHERE del_status = 'Live';

-- Tampilkan data yang sudah diperbaiki (sample)
SELECT 
    id,
    sale_no,
    sale_date,
    date_time,
    DATE(date_time) as calculated_date,
    CASE 
        WHEN sale_date = DATE(date_time) THEN 'MATCH' 
        ELSE 'MISMATCH' 
    END as status
FROM tbl_sales 
WHERE del_status = 'Live'
ORDER BY id DESC
LIMIT 20;

-- Jika masih ada yang MISMATCH, tampilkan untuk investigasi
SELECT 
    'REMAINING ISSUES' as note,
    id, sale_no, sale_date, date_time, DATE(date_time) as should_be
FROM tbl_sales 
WHERE del_status = 'Live' 
AND sale_date != DATE(date_time)
LIMIT 5;
