-- LANGKAH DEMI LANGKAH PERBAIKAN SALE_DATE
-- Jalankan satu per satu untuk memastikan setiap step berhasil

-- ========================================
-- STEP 1: BACKUP DATA (WAJIB!)
-- ========================================
CREATE TABLE tbl_sales_backup_20250811 AS 
SELECT * FROM tbl_sales WHERE del_status = 'Live';

SELECT 'BACKUP CREATED' as status, COUNT(*) as records_backed_up 
FROM tbl_sales_backup_20250811;

-- ========================================
-- STEP 2: CEK DATA SEBELUM PERBAIKAN
-- ========================================
SELECT 
    'BEFORE FIX - SUMMARY' as status,
    COUNT(*) as total_records,
    SUM(CASE WHEN sale_date = DATE(date_time) THEN 1 ELSE 0 END) as already_correct,
    SUM(CASE WHEN sale_date != DATE(date_time) THEN 1 ELSE 0 END) as need_fixing
FROM tbl_sales 
WHERE del_status = 'Live';

-- ========================================
-- STEP 3: TAMPILKAN CONTOH DATA BERMASALAH
-- ========================================
SELECT 
    'SAMPLE PROBLEMATIC DATA' as note,
    id, sale_no, sale_date, date_time, DATE(date_time) as correct_sale_date
FROM tbl_sales 
WHERE del_status = 'Live' 
AND sale_date != DATE(date_time)
ORDER BY id DESC 
LIMIT 5;

-- ========================================
-- STEP 4: JALANKAN PERBAIKAN
-- ========================================
UPDATE tbl_sales 
SET sale_date = DATE(date_time)
WHERE del_status = 'Live'
AND sale_date != DATE(date_time);

SELECT ROW_COUNT() as records_updated;

-- ========================================
-- STEP 5: VERIFIKASI HASIL
-- ========================================
SELECT 
    'AFTER FIX - SUMMARY' as status,
    COUNT(*) as total_records,
    SUM(CASE WHEN sale_date = DATE(date_time) THEN 1 ELSE 0 END) as now_correct,
    SUM(CASE WHEN sale_date != DATE(date_time) THEN 1 ELSE 0 END) as still_problematic
FROM tbl_sales 
WHERE del_status = 'Live';

-- ========================================
-- STEP 6: CEK JIKA MASIH ADA MASALAH
-- ========================================
SELECT 
    'REMAINING ISSUES' as note,
    id, sale_no, sale_date, date_time, DATE(date_time) as should_be
FROM tbl_sales 
WHERE del_status = 'Live' 
AND sale_date != DATE(date_time)
LIMIT 3;

-- ========================================
-- STEP 7: SAMPLE DATA SETELAH PERBAIKAN
-- ========================================
SELECT 
    'FIXED DATA SAMPLE' as note,
    id, sale_no, sale_date, date_time,
    CASE WHEN sale_date = DATE(date_time) THEN 'OK' ELSE 'ERROR' END as status
FROM tbl_sales 
WHERE del_status = 'Live'
ORDER BY id DESC 
LIMIT 10;
