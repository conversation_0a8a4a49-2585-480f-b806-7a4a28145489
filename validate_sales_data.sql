-- Script validasi untuk memastikan konsistensi data tbl_sales
-- Author: AI Assistant
-- Date: 2025-08-11

-- 1. Cek konsistensi antara sale_date dan date_time
SELECT 
    'Consistency Check' as check_type,
    COUNT(*) as total_records,
    SUM(CASE WHEN sale_date = DATE(date_time) THEN 1 ELSE 0 END) as consistent_records,
    SUM(CASE WHEN sale_date != DATE(date_time) THEN 1 ELSE 0 END) as inconsistent_records,
    ROUND((SUM(CASE WHEN sale_date = DATE(date_time) THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) as consistency_percentage
FROM tbl_sales 
WHERE del_status = 'Live';

-- 2. <PERSON><PERSON><PERSON>an data yang tidak konsisten (jika ada)
SELECT 
    id,
    sale_no,
    sale_date,
    date_time,
    DATE(date_time) as calculated_date,
    'INCONSISTENT' as status
FROM tbl_sales 
WHERE del_status = 'Live' 
AND sale_date != DATE(date_time)
ORDER BY id DESC
LIMIT 10;

-- 3. Cek data terbaru (hari ini dan kemarin)
SELECT
    'Recent Data Check' as check_type,
    DATE(date_time) as transaction_date,
    COUNT(*) as total_records,
    SUM(CASE WHEN sale_date = DATE(date_time) THEN 1 ELSE 0 END) as consistent_records,
    SUM(CASE WHEN sale_date != DATE(date_time) THEN 1 ELSE 0 END) as inconsistent_records
FROM tbl_sales
WHERE del_status = 'Live'
AND DATE(date_time) >= CURDATE() - INTERVAL 2 DAY
GROUP BY DATE(date_time)
ORDER BY transaction_date DESC;

-- 4. Analisis distribusi data per tanggal
SELECT 
    sale_date,
    COUNT(*) as transaction_count,
    MIN(date_time) as earliest_time,
    MAX(date_time) as latest_time
FROM tbl_sales 
WHERE del_status = 'Live'
GROUP BY sale_date
ORDER BY sale_date DESC
LIMIT 10;

-- 5. Cek data yang mungkin bermasalah (NULL values)
SELECT 
    'NULL Check' as check_type,
    COUNT(*) as total_records,
    SUM(CASE WHEN sale_date IS NULL THEN 1 ELSE 0 END) as null_sale_date,
    SUM(CASE WHEN date_time IS NULL THEN 1 ELSE 0 END) as null_date_time,
    SUM(CASE WHEN sale_date IS NULL OR date_time IS NULL THEN 1 ELSE 0 END) as total_null_records
FROM tbl_sales 
WHERE del_status = 'Live';

-- 6. Cek format sale_date yang tidak valid
SELECT 
    id,
    sale_no,
    sale_date,
    date_time,
    'INVALID_FORMAT' as issue
FROM tbl_sales 
WHERE del_status = 'Live'
AND (
    sale_date NOT REGEXP '^[0-9]{4}-[0-9]{2}-[0-9]{2}$'
    OR LENGTH(sale_date) != 10
)
LIMIT 10;
